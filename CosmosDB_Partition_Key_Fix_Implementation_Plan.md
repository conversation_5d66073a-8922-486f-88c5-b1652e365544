# CosmosDB Partition Key Mismatch Fix - Implementation Plan

## Problem Analysis

### Root Cause Identified
- **CosmosDB Container Configuration**: Partition key is `/TenantId` (capital T)
- **Document JSON Property**: `[JsonPropertyName("tenantId")]` (lowercase t)
- **Error**: CosmosDB expects `/TenantId` but document has `tenantId`

### Error Details
```
Microsoft.Azure.Cosmos.CosmosException : Response status code does not indicate success: BadRequest (400); Substatus: 1001; ActivityId: bd1f5345-0616-4158-9af9-3ec0d175678f; Reason: (Message: {"Errors":["PartitionKey extracted from document doesn't match the one specified in the header. Learn more: https://aka.ms/CosmosDB/sql/errors/wrong-pk-value"]}
```

## Solution: Approach 1 - Fix JSON Property Names

### Architecture Diagram
```mermaid
graph TB
    A[CosmosDB Container] --> B[Partition Key: /TenantId]
    C[Document JSON] --> D[Property: TenantId]
    B --> E[✅ Match]
    D --> E
    
    F[Current Issue] --> G[Property: tenantId]
    B --> H[❌ Mismatch]
    G --> H
    
    subgraph "Fix Implementation"
        I[Update JsonPropertyName] --> J["tenantId" → "TenantId"]
        J --> K[Add Validation Logging]
        K --> L[Enhanced Error Handling]
    end
```

## Implementation Steps

### Step 1: Update Document Models
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

#### Changes Required:

1. **VersionedReportDataDocument Class** (Line ~463)
   ```csharp
   // CHANGE FROM:
   [JsonPropertyName("tenantId")]
   public string TenantId { get; set; } = string.Empty;
   
   // CHANGE TO:
   [JsonPropertyName("TenantId")]  // Capital T to match partition key /TenantId
   public string TenantId { get; set; } = string.Empty;
   ```

2. **ReportDataDocument Class** (Line ~421)
   ```csharp
   // CHANGE FROM:
   [JsonPropertyName("tenantId")]
   public string TenantId { get; set; } = string.Empty;
   
   // CHANGE TO:
   [JsonPropertyName("TenantId")]  // Capital T to match partition key /TenantId
   public string TenantId { get; set; } = string.Empty;
   ```

3. **DocumentIdResult Class** (Line ~553)
   ```csharp
   // CHANGE FROM:
   [JsonPropertyName("tenantId")]
   public string TenantId { get; set; } = string.Empty;
   
   // CHANGE TO:
   [JsonPropertyName("TenantId")]  // Capital T to match partition key /TenantId
   public string TenantId { get; set; } = string.Empty;
   ```

### Step 2: Add Enhanced Validation Logging
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

#### Update CreateVersionedReportDocumentAsync Method (Line ~315)
Add validation logging after document creation:

```csharp
var document = new VersionedReportDataDocument
{
    Id = GenerateVersionedDocumentId(report.Id, version.Id),
    TenantId = tenantId,  // This will now serialize as "TenantId" (capital T)
    // ... other properties
};

// VALIDATION: Log the exact JSON that will be sent to CosmosDB
var jsonPreview = JsonSerializer.Serialize(new { 
    id = document.Id, 
    TenantId = document.TenantId 
}, JsonOptions);
_logger.LogDebug("Document JSON preview for partition key validation: {JsonPreview}", jsonPreview);

return document;
```

### Step 3: Enhanced Error Handling
**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

#### Update SeedAsync Method (Around Line 131)
Replace the existing upsert call with enhanced error handling:

```csharp
try
{
    // Validate partition key matches document property before upsert
    if (string.IsNullOrEmpty(document.TenantId))
    {
        throw new InvalidOperationException($"Document TenantId is null or empty for report {report.Id}");
    }

    _logger.LogDebug("Attempting to upsert document {DocumentId} with partition key '{PartitionKey}' for report {ReportId} version {VersionId}",
        document.Id, document.TenantId, report.Id, version.Id);

    await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
    
    _logger.LogDebug("Successfully upserted document {DocumentId} with partition key {PartitionKey}", 
        document.Id, document.TenantId);
}
catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.BadRequest && ex.SubStatusCode == 1001)
{
    _logger.LogError(ex, "Partition key mismatch for document {DocumentId}. " +
        "Document TenantId: '{DocumentTenantId}', Partition Key: '{PartitionKey}'. " +
        "Ensure JSON property name matches CosmosDB partition key path.",
        document.Id, document.TenantId, document.TenantId);
    throw;
}
catch (Exception ex)
{
    _logger.LogError(ex, "Error seeding report {ReportId} to Cosmos DB", report.Id);
    errorCount++;
    // Continue with other reports instead of failing completely
}
```

## Expected Results

### Before Fix
- Document JSON: `{"id":"report-data-123","tenantId":"guid-value",...}`
- CosmosDB looks for: `/TenantId`
- Result: ❌ Partition key mismatch error

### After Fix
- Document JSON: `{"id":"report-data-123","TenantId":"guid-value",...}`
- CosmosDB looks for: `/TenantId`
- Result: ✅ Successful document creation

## Verification Steps

1. **Run the application** - The seeding should complete without partition key errors
2. **Check logs** - Look for successful upsert messages and JSON preview logs
3. **Query CosmosDB** - Verify documents are created with correct partition key values
4. **Test multi-tenancy** - Confirm tenant isolation works properly

## Files to Modify

1. `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`
   - Update 3 JsonPropertyName attributes from "tenantId" to "TenantId"
   - Add validation logging in CreateVersionedReportDocumentAsync
   - Add enhanced error handling in SeedAsync method

## Implementation Priority

1. ✅ **High Priority**: Fix JsonPropertyName attributes (critical for partition key matching)
2. ✅ **Medium Priority**: Add validation logging (helps with debugging)
3. ✅ **Medium Priority**: Add enhanced error handling (improves error visibility)

This fix addresses the core issue while maintaining all existing functionality and improving observability for future debugging.