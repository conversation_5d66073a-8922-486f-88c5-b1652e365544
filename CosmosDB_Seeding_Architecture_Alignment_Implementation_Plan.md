# CosmosDB Seeding Architecture Alignment - Implementation Plan

## Overview
This plan addresses the "invalid field value" issue in CosmosDB seeding by aligning the seeding architecture with the multi-storage design and consolidating scattered seeding services.

## Current Issues
1. **Invalid field values** during CosmosDB seeding (root cause: data structure misalignment)
2. **Partition key inconsistency** between setup scripts and services
3. **Scattered seeding services** across multiple projects
4. **Data structure misalignment** with multi-storage architecture

## Storage Architecture (Corrected Understanding)
- **SQL Database**: Report metadata, references, and relationships
- **CosmosDB**: Report data (sections and fields as JSON documents)
- **Blob Storage**: Report styles, components, HTML/CSS templates

## Implementation Plan

### Phase 1: Fix Partition Key Consistency ⚡ CRITICAL
**Priority**: HIGH - Required for successful seeding

#### 1.1 Update CosmosDbSetup.cs
```csharp
// File: cosmosDBmaintenance/CosmosDbSetup.cs
// Change all container configurations from:
("ReportData", "/tenantId", 400)
// To:
("ReportData", "/TenantId", 400)
```

#### 1.2 Verify Document Models
Ensure all CosmosDB document models use:
```csharp
[JsonPropertyName("TenantId")]  // Capital T
public string TenantId { get; set; } = string.Empty;
```

**Files to check**:
- `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/StorageInitializationService.cs`

### Phase 2: Data Extraction and Analysis 📊
**Priority**: HIGH - Required for proper data consolidation

#### 2.1 Extract Report Data from BlobStorageSeeder.cs
**Target**: Report content data (sections, fields, mock data)
**Destination**: CosmosSeeder.cs

**Key data to extract**:
- Old mock data structure (`OldMockData` class)
- Dashboard metrics (today, wtd, mtd, ytd)
- Report-specific data creation logic
- Category mapping logic

#### 2.2 Extract Report Styles from CosmosDbSeeder.cs
**Target**: Report styling and presentation data
**Destination**: BlobSeeder.cs

**Key data to extract**:
- 6 report style mappings (TechCorp, Health Plus, TechFusion)
- HTML content generation (`GenerateReportHtml`)
- CSS styles generation (`GenerateReportCss`)
- Component styles (`GenerateComponentStyles`)

### Phase 3: Update CosmosSeeder.cs 🗄️
**Priority**: HIGH - Core seeding functionality

#### 3.1 Integrate Report Data Logic
Add extracted logic from BlobStorageSeeder.cs:
- Mock data loading and processing
- Dashboard metrics integration
- Report data structure creation
- Category-based data mapping

#### 3.2 Align Document Structure with Multi-Storage Design
Update document models to match the expected schema:
```csharp
// Expected CosmosDB document structure
{
  "id": "report-data-{reportId}-v{versionId}",
  "TenantId": "tenant-guid-string",
  "reportId": "report-guid",
  "versionId": "version-guid",
  "sections": [
    {
      "id": "section-guid",
      "title": "Section Title",
      "type": "text|chart|table",
      "order": 0,
      "fields": [
        {
          "id": "field-guid",
          "name": "field-name",
          "type": "string|number|json",
          "content": "field-content",
          "order": 0
        }
      ]
    }
  ],
  "metadata": {
    "createdAt": "timestamp",
    "updatedAt": "timestamp",
    "version": "1",
    "tags": ["category", "status"]
  }
}
```

#### 3.3 Add Enhanced Validation
Implement comprehensive field validation to prevent "invalid field value" errors:
- Pre-validation before document creation
- Field type validation
- Required field checks
- Data size validation

### Phase 4: Update BlobSeeder.cs 📁
**Priority**: MEDIUM - Styling and presentation

#### 4.1 Integrate Report Styles Logic
Add extracted logic from CosmosDbSeeder.cs:
- Report style mappings
- HTML template generation
- CSS styles generation
- Component definitions

#### 4.2 Align with Blob Storage Architecture
Ensure blob storage handles:
- HTML templates
- CSS stylesheets
- Component definitions
- Style metadata

### Phase 5: File Cleanup 🧹
**Priority**: MEDIUM - Code organization

#### 5.1 Remove Obsolete Files
Delete the following files after data extraction:
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/BlobStorageSeeder.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/CosmosDbSeeder.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/DataMigrationService.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/SeededDataResolutionService.cs`

#### 5.2 Update Service Registrations
Remove service registrations for deleted services from:
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/DependencyInjection.cs`

### Phase 6: Review Supporting Services 🔍
**Priority**: LOW - Architecture compliance

#### 6.1 Review Configuration Files
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Configuration/CosmosDbOptions.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Configuration/BlobStorageOptions.cs`

#### 6.2 Review Service Files
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/AzureBlobReportDataService.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/CosmosDbReportStyleService.cs`
- `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/StorageInitializationService.cs`

## Implementation Steps

### Step 1: Backup and Preparation
1. Create backup of current seeding files
2. Document current seeding behavior
3. Test current seeding to establish baseline

### Step 2: Partition Key Fix
1. Update CosmosDbSetup.cs partition key paths
2. Verify all document models use correct JsonPropertyName
3. Test partition key consistency

### Step 3: Data Extraction
1. Extract report data logic from BlobStorageSeeder.cs
2. Extract report styles logic from CosmosDbSeeder.cs
3. Create consolidated seed data files

### Step 4: CosmosSeeder Integration
1. Integrate extracted report data logic
2. Update document structure to match multi-storage design
3. Add enhanced validation
4. Test CosmosDB seeding

### Step 5: BlobSeeder Integration
1. Integrate extracted report styles logic
2. Align with blob storage architecture
3. Test blob storage seeding

### Step 6: Cleanup and Testing
1. Remove obsolete files
2. Update service registrations
3. Run comprehensive seeding tests
4. Validate data integrity across all storage types

## Success Criteria

### Technical Success
- [ ] No "invalid field value" errors during CosmosDB seeding
- [ ] Consistent partition key usage across all services
- [ ] All seeding services located in `/Seeders/` directory
- [ ] Document structure aligns with multi-storage design

### Functional Success
- [ ] CosmosDB contains proper report data (sections/fields)
- [ ] Blob Storage contains proper report styles (HTML/CSS)
- [ ] SQL contains proper report metadata
- [ ] Seeding completes without errors

### Architecture Success
- [ ] Clear separation of concerns between storage types
- [ ] Consolidated seeding logic
- [ ] Aligned with multi-storage architecture
- [ ] Maintainable and extensible codebase

## Risk Mitigation

### Data Loss Prevention
- Backup all existing seed data before changes
- Incremental implementation with testing at each step
- Rollback plan for each phase

### Validation Strategy
- Comprehensive testing after each phase
- Data integrity checks across storage types
- Performance validation for seeding operations

## Timeline Estimate
- **Phase 1**: 2-3 hours (partition key fixes)
- **Phase 2**: 3-4 hours (data extraction and analysis)
- **Phase 3**: 4-6 hours (CosmosSeeder updates)
- **Phase 4**: 2-3 hours (BlobSeeder updates)
- **Phase 5**: 1-2 hours (cleanup)
- **Phase 6**: 2-3 hours (review and testing)

**Total Estimated Time**: 14-21 hours

## Dependencies
- Access to Azure CosmosDB and Blob Storage
- Ability to run seeding operations
- Testing environment for validation

## Next Steps
1. Review and approve this implementation plan
2. Begin with Phase 1 (partition key fixes)
3. Proceed incrementally through each phase
4. Validate success criteria at each step
