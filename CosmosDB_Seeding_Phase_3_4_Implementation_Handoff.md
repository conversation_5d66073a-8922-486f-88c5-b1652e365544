# CosmosDB Seeding Architecture Alignment - Phase 3 & 4 Implementation Handoff

## Status Summary
**Phases 1-2 COMPLETED ✅**
- Phase 1: Fixed partition key consistency from `/tenantId` to `/TenantId` in CosmosDbSetup.cs
- Phase 2: Successfully extracted report styles logic from CosmosDbSeeder → integrated into BlobSeeder.cs

**Next: Phases 3-4 Implementation Required**

## Phase 3: Update CosmosSeeder.cs with Report Data Logic

### Objective
Extract report data logic from `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/BlobStorageSeeder.cs` and integrate it into `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs` to align with multi-storage architecture.

### Current State Analysis

#### Source File: `BlobStorageSeeder.cs` (ReportRenderingEngine)
**Location**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/BlobStorageSeeder.cs`

**Report Data Logic to Extract**:
1. **Dashboard Metrics Creation** (lines ~95-130)
   - `SeedDashboardMetricsAsync()` method
   - Creates dashboard data for each tenant with metrics (today, wtd, mtd, ytd)
   - Maps old mock data to tenant-specific dashboard metrics

2. **Report-Specific Data Creation** (lines ~135-180)
   - `SeedReportSpecificDataAsync()` method
   - Creates report data combining dashboard metrics with report-specific info
   - Category mapping from old mock reports to current categories
   - Report data structure with metadata

3. **Data Models** (lines ~300-400)
   - `SeededReportInfo` class
   - `SeededTenantInfo` class
   - `OldMockData` and related classes (MetricsPeriod, MetricsData, MetricValue, OldMockReport)

4. **Helper Methods**:
   - `LoadOldMockDataAsync()` - loads mock data from JSON files
   - `CreateCategoryMapping()` - maps old categories to new ones
   - `FindMatchingOldReport()` - finds matching reports for data correlation
   - `CreateReportData()` - creates comprehensive report data structure
   - `DetermineMetricsTimeframe()` - maps categories to timeframes
   - `GetMetricsByTimeframe()` - retrieves metrics by timeframe
   - `GenerateSampleChartData()` - creates sample chart data

#### Target File: `CosmosSeeder.cs` (Main Infrastructure)
**Location**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Current State**: Already has comprehensive report document creation logic but needs enhancement with the extracted report data logic.

### Implementation Steps for Phase 3

#### Step 3.1: Extract Data Models
Add the following classes to `CosmosSeeder.cs`:
```csharp
// Data transfer objects for seeding (extracted from BlobStorageSeeder)
public class SeededReportInfo
{
    public Guid ReportId { get; set; }
    public string ReportNumber { get; set; } = string.Empty;
    public string ReportName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public Guid TenantId { get; set; }
    public Guid ClientId { get; set; }
    public string ClientName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
    public int SlideCount { get; set; }
    public DateTime CreationTime { get; set; }
}

// Old mock data models (extracted from BlobStorageSeeder)
public class OldMockData
{
    public MetricsPeriod Today { get; set; } = new();
    public MetricsPeriod Wtd { get; set; } = new();
    public MetricsPeriod Mtd { get; set; } = new();
    public MetricsPeriod Ytd { get; set; } = new();
    public List<OldMockReport> Reports { get; set; } = new();
}

public class MetricsPeriod
{
    public MetricsData Metrics { get; set; } = new();
}

public class MetricsData
{
    public MetricValue TotalCustomers { get; set; } = new();
    public MetricValue NewCustomers { get; set; } = new();
    public MetricValue ReportsCreated { get; set; } = new();
    public MetricValue Revenue { get; set; } = new();
}

public class MetricValue
{
    public int Current { get; set; }
    public int PreviousPeriod { get; set; }
    public List<int> Trend { get; set; } = new();
}

public class OldMockReport
{
    public string ReportId { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientName { get; set; } = string.Empty;
    public string ReportName { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public int SlideCount { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
    public string LastModified { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Author { get; set; } = string.Empty;
}
```

#### Step 3.2: Add Mock Data Loading
Add method to load old mock data:
```csharp
private async Task<OldMockData?> LoadOldMockDataAsync()
{
    try
    {
        // Try multiple possible paths for the old mock data file
        var possiblePaths = new[]
        {
            Path.Combine("memory-bank", "old_json_data", "src", "data", "db", "db.json"),
            Path.Combine("..", "memory-bank", "old_json_data", "src", "data", "db", "db.json"),
            Path.Combine("..", "..", "memory-bank", "old_json_data", "src", "data", "db", "db.json"),
            Path.Combine(Directory.GetCurrentDirectory(), "..", "memory-bank", "old_json_data", "src", "data", "db", "db.json")
        };

        string? foundPath = null;
        foreach (var path in possiblePaths)
        {
            if (File.Exists(path))
            {
                foundPath = path;
                break;
            }
        }

        if (foundPath == null)
        {
            _logger.LogWarning("Old mock data file not found. Tried paths: {Paths}", string.Join(", ", possiblePaths));
            return null;
        }

        var jsonContent = await File.ReadAllTextAsync(foundPath);
        var mockData = JsonSerializer.Deserialize<OldMockData>(jsonContent, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        _logger.LogInformation("Successfully loaded old mock data from {FilePath}", foundPath);
        return mockData;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error loading old mock data");
        return null;
    }
}
```

#### Step 3.3: Enhance Document Creation
Modify `CreateVersionedReportDocumentAsync` to include dashboard metrics and enhanced report data:

1. Load old mock data at the beginning of the seeding process
2. Create category mapping for correlating old data with new reports
3. Enhance the document creation to include:
   - Dashboard metrics based on report category
   - Sample chart data
   - Enhanced metadata with metrics information

#### Step 3.4: Add Helper Methods
Add the following helper methods:
- `CreateCategoryMapping()` - maps old categories to new ones
- `FindMatchingOldReport()` - finds matching reports for data correlation
- `DetermineMetricsTimeframe()` - maps categories to timeframes
- `GetMetricsByTimeframe()` - retrieves metrics by timeframe
- `GenerateSampleChartData()` - creates sample chart data

### Expected Outcome for Phase 3
- CosmosSeeder.cs will contain comprehensive report data logic
- Documents will include dashboard metrics and enhanced data
- Report data will be properly aligned with multi-storage architecture
- Old mock data will be properly integrated for realistic seeding

---

## Phase 4: Finalize BlobSeeder.cs Enhancements

### Objective
Complete any remaining enhancements to BlobSeeder.cs and ensure full alignment with multi-storage architecture.

### Current State Analysis
**Phase 2 completed**: Report styles logic successfully extracted and integrated into BlobSeeder.cs

### Implementation Steps for Phase 4

#### Step 4.1: Review and Optimize Style Generation
- Verify all style generation methods are working correctly
- Ensure proper tenant-to-style mapping
- Optimize HTML/CSS generation for performance

#### Step 4.2: Add Missing Blob Service Integration
Currently the BlobSeeder has TODO comments for actual blob storage service integration:
```csharp
// TODO: When blob storage service is available, save the blob
```

**Action Required**: 
1. Check if blob storage services are available in the project
2. If available, integrate actual blob saving functionality
3. If not available, ensure proper simulation and logging

#### Step 4.3: Enhance Error Handling
- Add comprehensive validation for style data
- Improve error recovery mechanisms
- Add detailed logging for troubleshooting

#### Step 4.4: Add Blob Retrieval Implementation
Complete the `GetExistingBlobIdsAsync` method:
```csharp
public async Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default)
{
    // TODO: Implement blob ID retrieval when blob storage services are available
}
```

### Expected Outcome for Phase 4
- BlobSeeder.cs will be fully functional with complete style generation
- Proper blob storage integration (or simulation)
- Enhanced error handling and validation
- Complete alignment with multi-storage architecture

---

## Implementation Priority and Dependencies

### Critical Path
1. **Phase 3 MUST be completed first** - CosmosSeeder needs the report data logic
2. **Phase 4 can be done in parallel** - BlobSeeder enhancements are independent

### Key Files to Modify

#### Phase 3:
- **Primary**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`
- **Reference**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/BlobStorageSeeder.cs`

#### Phase 4:
- **Primary**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/BlobSeeder.cs`

### Testing Strategy
After each phase:
1. Verify compilation with no errors
2. Test seeding process with sample data
3. Validate partition key consistency
4. Check multi-storage alignment

### Risk Mitigation
- **Backup current files** before making changes
- **Test incrementally** - implement one method at a time
- **Validate data structures** match between extracted and target files
- **Ensure proper error handling** for missing dependencies

---

## Success Criteria

### Phase 3 Success Criteria:
- [ ] CosmosSeeder.cs contains extracted report data logic
- [ ] Documents include dashboard metrics and enhanced data
- [ ] Old mock data integration works correctly
- [ ] No compilation errors
- [ ] Seeding process creates comprehensive report documents

### Phase 4 Success Criteria:
- [ ] BlobSeeder.cs has complete style generation functionality
- [ ] Proper blob storage integration (or simulation)
- [ ] Enhanced error handling and validation
- [ ] No compilation errors
- [ ] Seeding process creates proper style blobs

### Overall Success Criteria:
- [ ] No "invalid field value" errors during seeding
- [ ] Consistent partition keys across all containers
- [ ] Proper multi-storage architecture alignment
- [ ] Report data in CosmosDB, styles/components in Blob Storage
- [ ] Comprehensive logging and error handling

---

## Next Steps After Phase 3-4 Completion

Once Phases 3-4 are complete, proceed with:
- **Phase 5**: File cleanup and service registration updates
- **Phase 6**: Review supporting services for architecture compliance
- **Testing**: End-to-end seeding validation
- **Documentation**: Update architecture documentation

---

## Contact and Support
This handoff document provides the complete roadmap for implementing Phases 3-4. The foundation from Phases 1-2 ensures that partition key issues are resolved and the architectural alignment is properly established.
