# CosmosDB Seeding Phase 6 - Architecture Compliance Implementation Plan

## Objective
Resolve the critical architecture violation identified in `CosmosDbReportStyleService` and ensure full alignment with the multi-storage architecture.

## Storage Architecture Recap
- **SQL Database**: Report metadata and style selections
- **Azure Blob Storage**: Rendered Next.js components (HTML/CSS)
- **Azure Cosmos DB**: Report data (sections and fields as JSON)

## Implementation Steps

### Step 1: Critical Architecture Assessment
- **Impact Analysis**: Identify all usages of `CosmosDbReportStyleService`.
- **Data Assessment**: Confirm no production data migration required; assess current mock data structure.
- **Architecture Alignment Evaluation**: Ensure all services and seeders strictly follow the storage architecture.

**Work executed**:
Executed command
```powershell
PS C:\Users\<USER>\source\repos\fy-wb-cshero2> Get-ChildItem -Recurse -Include "*.cs" | Select-String "CosmosDbReportStyleService|IReportStyleService"
```
Based on the provided search output, the following changes are required:
1. Replace the implementation of `IReportStyleService` from `CosmosDbReportStyleService` to `BlobReportStyleService`.
2. Update the service registration in `DependencyInjection.cs`.
I'll start by updating the service registration in `DependencyInjection.cs` to use the new `BlobReportStyleService`.

- The update was executed

Next, the new BlobReportStyleService needs to be implemented. To proceed, we need to verify if this service already exists or if it needs to be created from scratch. I'll check the relevant directory for its existence first.

### Step 2: Service Refactoring
- **Recommended Approach**: Implement `BlobReportStyleService` to replace `CosmosDbReportStyleService`.
- **Alternative Approaches**: Document and evaluate hybrid or removal options if necessary.

### Step 3: Configuration Alignment
- **CosmosDbOptions.cs**: Validate partition key (`/TenantId`) and container configurations; remove obsolete `ReportStyles` container after migration.
- **BlobStorageOptions.cs**: Confirm blob storage paths and hierarchy match seeder expectations.

### Step 4: Seeder Alignment
- Update seeders to strictly follow storage architecture:
  - **SqlSeeder**: Metadata and style selections
  - **BlobSeeder**: HTML/CSS components
  - **CosmosSeeder**: Report data (sections/fields only)

### Step 5: Integration and Testing
- **End-to-End Workflow Validation**: Storage initialization → Seeding → Style operations.
- **Cross-Service Compatibility Testing**: Validate interactions between SQL, Blob, and CosmosDB services.

### Step 6: Documentation and Cleanup
- Update documentation to reflect new architecture.
- Remove obsolete services and configurations.

## Deliverables
- [ ] **Architecture Violation Resolution Plan**
- [ ] **BlobReportStyleService Implementation**
- [ ] **Updated Seeder Processes**
- [ ] **Configuration Review Report**
- [ ] **Integration Testing Results**
- [ ] **Updated Documentation**

## Success Criteria
- CosmosDbReportStyleService violation fully resolved.
- All services and seeders strictly adhere to the storage architecture.
- Comprehensive testing completed successfully.
- Documentation accurately reflects the new architecture.

## Risk Mitigation
- Maintain interface compatibility during refactoring.
- Thorough testing at each implementation step.
- Performance validation and optimization.

## Timeline Estimate
- **Critical Path**: 12-16 hours
- **Standard Tasks**: 8-12 hours
- **Total Estimated Time**: 20-28 hours
