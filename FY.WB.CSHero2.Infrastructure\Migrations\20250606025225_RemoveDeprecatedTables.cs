﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    /// <summary>
    /// Removes deprecated tables: Forms, Invoices, and Uploads
    /// These tables are no longer used in the application and can be safely removed
    /// </summary>
    public partial class RemoveDeprecatedTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop indexes first (dependency order)
            migrationBuilder.DropIndex(
                name: "IX_Forms_Category",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Category_Priority_Date",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Date",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Email",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Priority",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_TenantId",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_OrderNumber",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_TenantId",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_ContentType",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename_StoragePath",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename_StorageProvider",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_StorageProvider",
                table: "Uploads");

            // Drop tables (no dependencies between these deprecated tables)
            migrationBuilder.DropTable(
                name: "ComponentDefinitions");

            migrationBuilder.DropTable(
                name: "Forms");

            migrationBuilder.DropTable(
                name: "Invoices");

            migrationBuilder.DropTable(
                name: "Uploads");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Recreate tables if rollback needed
            // Note: This recreates the deprecated tables for rollback safety
            
            // Forms table recreation
            migrationBuilder.CreateTable(
                name: "Forms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CustomerName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Priority = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Forms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Forms_TenantProfiles_TenantId",
                        column: x => x.TenantId,
                        principalTable: "TenantProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Invoices table recreation
            migrationBuilder.CreateTable(
                name: "Invoices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OrderNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Plans = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoices_TenantProfiles_TenantId",
                        column: x => x.TenantId,
                        principalTable: "TenantProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Uploads table recreation
            migrationBuilder.CreateTable(
                name: "Uploads",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Filename = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Size = table.Column<long>(type: "bigint", nullable: false),
                    ContentType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StoragePath = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    StorageProvider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ExternalUrl = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Checksum = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Uploads", x => x.Id);
                    table.CheckConstraint("CK_Upload_Size", "[Size] >= 0");
                });

            // Recreate indexes
            migrationBuilder.CreateIndex(
                name: "IX_Forms_Category",
                table: "Forms",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_Forms_Category_Priority_Date",
                table: "Forms",
                columns: new[] { "Category", "Priority", "Date" });

            migrationBuilder.CreateIndex(
                name: "IX_Forms_Date",
                table: "Forms",
                column: "Date");

            migrationBuilder.CreateIndex(
                name: "IX_Forms_Email",
                table: "Forms",
                column: "Email");

            migrationBuilder.CreateIndex(
                name: "IX_Forms_Priority",
                table: "Forms",
                column: "Priority");

            migrationBuilder.CreateIndex(
                name: "IX_Forms_TenantId",
                table: "Forms",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_OrderNumber",
                table: "Invoices",
                column: "OrderNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_TenantId",
                table: "Invoices",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_Uploads_ContentType",
                table: "Uploads",
                column: "ContentType");

            migrationBuilder.CreateIndex(
                name: "IX_Uploads_Filename",
                table: "Uploads",
                column: "Filename");

            migrationBuilder.CreateIndex(
                name: "IX_Uploads_Filename_StoragePath",
                table: "Uploads",
                columns: new[] { "Filename", "StoragePath" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Uploads_Filename_StorageProvider",
                table: "Uploads",
                columns: new[] { "Filename", "StorageProvider" });

            migrationBuilder.CreateIndex(
                name: "IX_Uploads_StorageProvider",
                table: "Uploads",
                column: "StorageProvider");
        }
    }
}
