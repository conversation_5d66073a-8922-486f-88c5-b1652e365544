{"today": {"metrics": {"totalCustomers": {"current": 150, "previousPeriod": 145, "trend": [140, 142, 145, 143, 147, 149, 150]}, "newCustomers": {"current": 5, "previousPeriod": 3, "trend": [2, 3, 2, 4, 3, 4, 5]}, "reportsCreated": {"current": 25, "previousPeriod": 20, "trend": [18, 20, 22, 21, 23, 24, 25]}, "revenue": {"current": 2500, "previousPeriod": 2200, "trend": [2100, 2200, 2300, 2250, 2400, 2450, 2500]}}}, "wtd": {"metrics": {"totalCustomers": {"current": 160, "previousPeriod": 150, "trend": [145, 148, 150, 153, 155, 158, 160]}, "newCustomers": {"current": 15, "previousPeriod": 12, "trend": [10, 11, 12, 13, 14, 14, 15]}, "reportsCreated": {"current": 120, "previousPeriod": 100, "trend": [95, 98, 102, 105, 110, 115, 120]}, "revenue": {"current": 12000, "previousPeriod": 10000, "trend": [9500, 10000, 10500, 11000, 11200, 11500, 12000]}}}, "mtd": {"metrics": {"totalCustomers": {"current": 200, "previousPeriod": 180, "trend": [170, 175, 180, 185, 190, 195, 200]}, "newCustomers": {"current": 45, "previousPeriod": 35, "trend": [30, 33, 36, 38, 40, 42, 45]}, "reportsCreated": {"current": 450, "previousPeriod": 400, "trend": [380, 390, 400, 410, 420, 435, 450]}, "revenue": {"current": 45000, "previousPeriod": 40000, "trend": [38000, 39000, 40000, 41000, 42000, 43500, 45000]}}}, "ytd": {"metrics": {"totalCustomers": {"current": 1200, "previousPeriod": 1000, "trend": [950, 1000, 1050, 1100, 1150, 1180, 1200]}, "newCustomers": {"current": 350, "previousPeriod": 300, "trend": [280, 290, 300, 310, 320, 335, 350]}, "reportsCreated": {"current": 5200, "previousPeriod": 4800, "trend": [4600, 4700, 4800, 4900, 5000, 5100, 5200]}, "revenue": {"current": 520000, "previousPeriod": 480000, "trend": [460000, 470000, 480000, 490000, 500000, 510000, 520000]}}}, "reports": [{"reportId": "CSR-2025-001", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Q1 Customer Satisfaction Analysis", "category": "Satisfaction Survey", "slideCount": 12, "createdAt": "2025-01-15T10:30:00Z", "lastModified": "2025-01-18T14:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-002", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Support Ticket Resolution Time Analysis", "category": "Performance Metrics", "slideCount": 8, "createdAt": "2025-01-20T09:15:00Z", "lastModified": "2025-01-22T16:20:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-003", "clientId": "CLIENT-001", "clientName": "Acme Corporation", "reportName": "Customer Feedback Implementation Plan", "category": "Action Plan", "slideCount": 15, "createdAt": "2025-01-25T13:45:00Z", "lastModified": "2025-02-10T11:30:00Z", "status": "In Progress", "author": "<PERSON>"}, {"reportId": "CSR-2025-004", "clientId": "CLIENT-003", "clientName": "Global Shipping Partners", "reportName": "Support Channel Effectiveness", "category": "Channel Analysis", "slideCount": 10, "createdAt": "2025-02-05T08:20:00Z", "lastModified": "2025-02-15T17:10:00Z", "status": "Under Review", "author": "<PERSON>"}, {"reportId": "CSR-2025-005", "clientId": "CLIENT-004", "clientName": "Pinnacle Healthcare", "reportName": "Customer Onboarding Experience", "category": "User Experience", "slideCount": 14, "createdAt": "2025-02-12T11:05:00Z", "lastModified": "2025-02-20T09:45:00Z", "status": "Completed", "author": "<PERSON>"}, {"reportId": "CSR-2025-006", "clientId": "CLIENT-002", "clientName": "TechFusion Inc.", "reportName": "Product Support Gap Analysis", "category": "Product Improvement", "slideCount": 18, "createdAt": "2025-02-18T14:30:00Z", "lastModified": "2025-02-22T16:15:00Z", "status": "Draft", "author": "<PERSON>"}], "categoryMappings": {"Satisfaction Survey": "<PERSON><PERSON><PERSON>", "Performance Metrics": "Performance", "Action Plan": "Strategy", "Channel Analysis": "Usage", "User Experience": "Usage", "Product Improvement": "Performance"}, "timeframeMappings": {"Feedback": "today", "Performance": "wtd", "Strategy": "mtd", "Usage": "ytd"}, "chartConfigurations": {"Feedback": {"chartType": "pie", "colors": ["#10b981", "#3b82f6", "#f59e0b"], "dataPointCount": 7}, "Performance": {"chartType": "line", "colors": ["#3b82f6", "#1d4ed8"], "dataPointCount": 7}, "Strategy": {"chartType": "bar", "colors": ["#f59e0b", "#d97706"], "dataPointCount": 7}, "Usage": {"chartType": "area", "colors": ["#8b5cf6", "#7c3aed"], "dataPointCount": 7}}}