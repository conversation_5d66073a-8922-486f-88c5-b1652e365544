using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Services;
using Microsoft.Azure.Cosmos;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface ICosmosSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default);
    }

    public class CosmosSeeder : ICosmosSeeder
    {
        private readonly ICosmosDbService? _cosmosDbService;
        private readonly ILogger<CosmosSeeder> _logger;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public CosmosSeeder(ICosmosDbService? cosmosDbService, ILogger<CosmosSeeder> logger)
        {
            _cosmosDbService = cosmosDbService;
            _logger = logger;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting Cosmos DB seeding...");

            if (_cosmosDbService == null)
            {
                _logger.LogWarning("Cosmos DB service not available, skipping Cosmos DB seeding");
                return;
            }

            try
            {
                // Load old mock data for enhanced seeding
                var oldMockData = await LoadOldMockDataAsync();
                if (oldMockData != null)
                {
                    _logger.LogInformation("Successfully loaded old mock data with {ReportCount} reports", oldMockData.Reports.Count);
                }
                else
                {
                    _logger.LogWarning("Could not load old mock data, proceeding with basic seeding");
                }

                // Validate that active tenants exist before proceeding
                var activeTenants = await context.TenantProfiles
                    .IgnoreQueryFilters()
                    .Where(tp => tp.TenantId != null)
                    .Select(tp => tp.TenantId.ToString())
                    .ToListAsync(cancellationToken);

                if (!activeTenants.Any())
                {
                    _logger.LogError("No tenants found in TenantProfiles table - cannot proceed with Cosmos seeding");
                    return;
                }
                _logger.LogInformation("Found {TenantCount} tenants for Cosmos seeding", activeTenants.Count);

                // Get existing document IDs to avoid duplicates
                var existingDocumentIds = await GetExistingDocumentIdsAsync(cancellationToken);
                _logger.LogInformation("Found {ExistingCount} existing documents in Cosmos DB", existingDocumentIds.Count);

                // Get all reports with their sections and fields from SQL
                var reports = await GetReportsWithDataAsync(context, cancellationToken);
                _logger.LogInformation("Found {ReportCount} reports to process for Cosmos DB seeding", reports.Count);

                // Create category mapping if old mock data is available
                Dictionary<string, List<OldMockReport>>? categoryMapping = null;
                if (oldMockData != null && oldMockData.Reports.Any())
                {
                    categoryMapping = CreateCategoryMapping(oldMockData.Reports);
                    _logger.LogInformation("Created category mapping with {CategoryCount} categories", categoryMapping.Count);
                }

                var createdCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var report in reports)
                {
                    try
                    {
                        // Get report versions for version-aware document creation
                        var reportVersions = await context.ReportVersions
                            .IgnoreQueryFilters()
                            .Where(rv => rv.ReportId == report.Id)
                            .OrderByDescending(rv => rv.VersionNumber)
                            .ToListAsync(cancellationToken);

                        // Create documents for each version
                        foreach (var version in reportVersions)
                        {
                            var versionedDocumentId = GenerateVersionedDocumentId(report.Id, version.Id);

                            if (existingDocumentIds.Contains(versionedDocumentId))
                            {
                                _logger.LogDebug("Skipping report {ReportId} version {VersionId} - document already exists",
                                    report.Id, version.Id);
                                skippedCount++;
                                continue;
                            }

                            // Validate TenantId before creating document
                            if (!report.TenantId.HasValue || report.TenantId.Value == Guid.Empty)
                            {
                                _logger.LogWarning("Skipping report {ReportId} '{ReportName}' - invalid TenantId", 
                                    report.Id, report.Name);
                                errorCount++;
                                continue;
                            }

                            var tenantIdString = report.TenantId.Value.ToString();
                            if (!activeTenants.Contains(tenantIdString))
                            {
                                _logger.LogWarning("Skipping report {ReportId} '{ReportName}' - TenantId {TenantId} not found in active tenants", 
                                    report.Id, report.Name, tenantIdString);
                                errorCount++;
                                continue;
                            }

                            // Create version-aware document from report data with enhanced mock data integration
                            var document = await CreateVersionedReportDocumentAsync(context, report, version, oldMockData, categoryMapping, cancellationToken);

                            // Final validation - this should not happen now but keeping as safety check
                            if (string.IsNullOrEmpty(document.TenantId) || document.TenantId == "default")
                            {
                                _logger.LogWarning("Skipping report {ReportId} - document has invalid TenantId: {TenantId}",
                                    report.Id, document.TenantId);
                                errorCount++;
                                continue;
                            }

                            // Validate partition key matches document property before upsert
                            if (string.IsNullOrEmpty(document.TenantId))
                            {
                                _logger.LogError("Document TenantId is null or empty for report {ReportId}", report.Id);
                                errorCount++;
                                continue;
                            }

                            // Log partition key details for debugging
                            _logger.LogDebug("Attempting to upsert document {DocumentId} with partition key '{PartitionKey}' for report {ReportId} version {VersionId}",
                                document.Id, document.TenantId, report.Id, version.Id);

                            try
                            {
                                // Save to Cosmos DB using document's TenantId as partition key
                                // Ensure the partition key passed matches exactly what's in the document
                                await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
                                
                                _logger.LogDebug("Successfully upserted document {DocumentId} with partition key {PartitionKey}",
                                    document.Id, document.TenantId);
                            }
                            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.BadRequest && ex.SubStatusCode == 1001)
                            {
                                _logger.LogError(ex, "Partition key mismatch for document {DocumentId}. " +
                                    "Document TenantId: '{DocumentTenantId}', Partition Key: '{PartitionKey}'. " +
                                    "Ensure JSON property name matches CosmosDB partition key path.",
                                    document.Id, document.TenantId, document.TenantId);
                                errorCount++;
                                continue;
                            }
                            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.BadRequest && ex.SubStatusCode == 0)
                            {
                                _logger.LogError(ex, "CosmosDB validation error for document {DocumentId}. " +
                                    "Error: 'One of the specified inputs is invalid'. " +
                                    "This typically indicates invalid field values, document structure, or size issues. " +
                                    "Check the validation logs above for specific field issues.",
                                    document.Id);
                                errorCount++;
                                continue;
                            }
                            catch (CosmosException ex)
                            {
                                _logger.LogError(ex, "CosmosDB error for document {DocumentId}. " +
                                    "StatusCode: {StatusCode}, SubStatusCode: {SubStatusCode}",
                                    document.Id, ex.StatusCode, ex.SubStatusCode);
                                errorCount++;
                                continue;
                            }

                            // Update SQL ReportVersion entity with document ID
                            await UpdateReportVersionWithDocumentIdAsync(context, version.Id, versionedDocumentId, cancellationToken);

                            // Update storage metadata
                            await UpdateStorageMetadataAsync(context, report.Id, document, cancellationToken);

                            createdCount++;
                            _logger.LogDebug("Created Cosmos document for report {ReportId} version {VersionId} with partition key {PartitionKey}",
                                report.Id, version.Id, document.TenantId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error seeding report {ReportId} to Cosmos DB", report.Id);
                        errorCount++;
                        // Continue with other reports instead of failing completely
                    }
                }

                _logger.LogInformation("Cosmos DB seeding completed: {Created} created, {Skipped} skipped, {Errors} errors", 
                    createdCount, skippedCount, errorCount);
                    
                if (errorCount > 0)
                {
                    _logger.LogWarning("Cosmos DB seeding completed with {ErrorCount} errors. Check logs for details.", errorCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Critical error during Cosmos DB seeding");
                throw;
            }
        }

        public async Task<List<string>> GetExistingDocumentIdsAsync(CancellationToken cancellationToken = default)
        {
            if (_cosmosDbService == null)
            {
                _logger.LogDebug("Cosmos DB service not available, returning empty document list");
                return new List<string>();
            }

            try
            {
                // Query all documents across all partitions to get their IDs (including versioned documents)
                var query = "SELECT c.id, c.tenantId FROM c WHERE STARTSWITH(c.id, 'report-data-')";
                var documents = await _cosmosDbService.GetItemsAsync<DocumentIdResult>(query);

                var documentIds = documents.Select(d => d.Id).ToList();
                _logger.LogDebug("Found {Count} existing documents (including versioned) across all partitions", documentIds.Count);

                return documentIds;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting existing document IDs from Cosmos DB, assuming empty");
                return new List<string>();
            }
        }

        private async Task<List<Report>> GetReportsWithDataAsync(ApplicationDbContext context, CancellationToken cancellationToken)
        {
            // Get reports and their sections separately due to navigation property structure
            var reports = await context.Reports
                .IgnoreQueryFilters()
                .ToListAsync(cancellationToken);

            // Load sections for each report
            foreach (var report in reports)
            {
                var sections = await context.ReportSections
                    .IgnoreQueryFilters()
                    .Where(rs => rs.ReportId == report.Id)
                    .Include(rs => rs.Fields)
                    .ToListAsync(cancellationToken);
                
                // Manually set the sections collection
                report.Sections = sections;
            }

            return reports;
        }

        private async Task<ReportDataDocument> CreateReportDocumentAsync(ApplicationDbContext context, Report report, CancellationToken cancellationToken)
        {
            // Get report versions for this report
            var reportVersions = await context.ReportVersions
                .IgnoreQueryFilters()
                .Where(rv => rv.ReportId == report.Id)
                .ToListAsync(cancellationToken);

            // Get the latest version or create a default one
            var latestVersion = reportVersions.OrderByDescending(rv => rv.VersionNumber).FirstOrDefault();
            var versionId = latestVersion?.Id ?? Guid.NewGuid();

            // Ensure TenantId is properly handled and consistent
            string tenantId;
            if (report.TenantId.HasValue && report.TenantId.Value != Guid.Empty)
            {
                tenantId = report.TenantId.Value.ToString();
                _logger.LogDebug("Report {ReportId} using TenantId: {TenantId}", report.Id, tenantId);
            }
            else
            {
                tenantId = "default";
                _logger.LogWarning("Report {ReportId} has null/empty TenantId, using 'default'. Original TenantId: {OriginalTenantId}", 
                    report.Id, report.TenantId);
            }

            // Transform sections and fields into document format
            var sections = report.Sections?.Select(section => new ReportDataSection
            {
                Id = section.Id,
                Name = section.Title, // ReportSection uses Title, not Name
                Title = section.Title,
                Description = string.Empty, // ReportSection doesn't have Description
                SectionType = section.Type, // ReportSection uses Type, not SectionType
                DisplayOrder = section.Order, // ReportSection uses Order, not DisplayOrder
                IsRequired = false, // ReportSection doesn't have IsRequired
                Fields = section.Fields?.Select(field => new ReportDataField
                {
                    Id = field.Id,
                    Name = field.Name,
                    Label = field.Name, // ReportSectionField doesn't have Label, use Name
                    FieldType = field.Type, // ReportSectionField uses Type, not FieldType
                    DefaultValue = field.Content ?? string.Empty, // ReportSectionField uses Content, not DefaultValue
                    IsRequired = false, // ReportSectionField doesn't have IsRequired
                    DisplayOrder = field.Order, // ReportSectionField uses Order, not DisplayOrder
                    ValidationRules = new Dictionary<string, object>(), // ReportSectionField doesn't have ValidationRules
                    Options = new List<string>() // ReportSectionField doesn't have Options
                }).ToList() ?? new List<ReportDataField>()
            }).ToList() ?? new List<ReportDataSection>();

            return new ReportDataDocument
            {
                Id = GenerateDocumentId(report.Id),
                TenantId = tenantId, // Fixed: Use string tenantId as partition key
                ReportId = report.Id,
                VersionId = versionId,
                ReportName = report.Name,
                ReportNumber = report.ReportNumber,
                Category = report.Category,
                Status = report.Status,
                Author = report.Author,
                ClientId = report.ClientId,
                ClientName = report.ClientName,
                Sections = sections,
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = report.CreationTime,
                    UpdatedAt = report.LastModificationTime ?? report.CreationTime,
                    SectionCount = sections.Count,
                    FieldCount = sections.Sum(s => s.Fields.Count),
                    Version = latestVersion?.VersionNumber.ToString() ?? "1",
                    Tags = new List<string> { report.Category, report.Status }
                }
            };
        }

        private async Task UpdateReportWithDocumentIdAsync(ApplicationDbContext context, Guid reportId, string documentId, CancellationToken cancellationToken)
        {
            var report = await context.Reports
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report != null)
            {
                report.DataDocumentId = documentId;
                await context.SaveChangesAsync(cancellationToken);
            }
        }

        private static string GenerateDocumentId(Guid reportId)
        {
            return $"report-data-{reportId}";
        }

        private static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
        {
            return $"report-data-{reportId}-v{versionId}";
        }

        private async Task<VersionedReportDataDocument> CreateVersionedReportDocumentAsync(
            ApplicationDbContext context,
            Report report,
            ReportVersion version,
            OldMockData? oldMockData,
            Dictionary<string, List<OldMockReport>>? categoryMapping,
            CancellationToken cancellationToken)
        {
            // TenantId validation is now done before calling this method
            // This method assumes report.TenantId is valid
            var tenantId = report.TenantId!.Value.ToString();
            _logger.LogDebug("Report {ReportId} using TenantId: {TenantId}", report.Id, tenantId);

            // Transform sections and fields into document format
            var sections = report.Sections?.Select(section => new ReportDataSection
            {
                Id = section.Id,
                Name = section.Title,
                Title = section.Title,
                Description = string.Empty,
                SectionType = section.Type,
                DisplayOrder = section.Order,
                IsRequired = false,
                Fields = section.Fields?.Select(field => new ReportDataField
                {
                    Id = field.Id,
                    Name = field.Name,
                    Label = field.Name,
                    FieldType = field.Type,
                    DefaultValue = field.Content ?? string.Empty,
                    IsRequired = false,
                    DisplayOrder = field.Order,
                    ValidationRules = new Dictionary<string, object>(),
                    Options = new List<string>()
                }).ToList() ?? new List<ReportDataField>()
            }).ToList() ?? new List<ReportDataSection>();

            // Enhanced document creation with old mock data integration
            var document = new VersionedReportDataDocument
            {
                Id = GenerateVersionedDocumentId(report.Id, version.Id),
                TenantId = tenantId,
                ReportId = report.Id,
                VersionId = version.Id,
                VersionNumber = version.VersionNumber,
                IsDraft = false, // Seed data represents saved versions
                ReportName = report.Name,
                ReportNumber = report.ReportNumber,
                Category = report.Category,
                Status = report.Status,
                Author = report.Author,
                ClientId = report.ClientId,
                ClientName = report.ClientName,
                Sections = sections,
                ComponentDataJson = version.ComponentDataJson,
                JsonData = version.JsonData,
                Metadata = new ReportDataMetadata
                {
                    CreatedAt = version.CreationTime,
                    UpdatedAt = version.LastModificationTime ?? version.CreationTime,
                    SectionCount = sections.Count,
                    FieldCount = sections.Sum(s => s.Fields.Count),
                    Version = version.VersionNumber.ToString(),
                    Tags = new List<string> { report.Category, report.Status }
                }
            };

            // Enhance document with old mock data if available
            if (oldMockData != null && categoryMapping != null)
            {
                try
                {
                    // Find matching old report for data correlation
                    var matchingOldReport = FindMatchingOldReport(report, categoryMapping);
                    
                    // Determine appropriate metrics timeframe based on category
                    var metricsTimeframe = DetermineMetricsTimeframe(report.Category);
                    var dashboardMetrics = GetMetricsByTimeframe(oldMockData, metricsTimeframe);
                    
                    // Generate sample chart data
                    var chartData = GenerateSampleChartData(report.Category);
                    
                    // Create enhanced JSON data with dashboard metrics and chart data
                    var enhancedData = new Dictionary<string, object>
                    {
                        ["reportId"] = report.Id,
                        ["reportNumber"] = report.ReportNumber,
                        ["reportName"] = report.Name,
                        ["category"] = report.Category,
                        ["tenantId"] = report.TenantId,
                        ["clientId"] = report.ClientId,
                        ["clientName"] = report.ClientName,
                        ["status"] = report.Status,
                        ["author"] = report.Author,
                        ["createdAt"] = version.CreationTime,
                        ["generatedAt"] = DateTime.UtcNow,
                        ["dataSource"] = "seeded_from_old_mock_data",
                        ["dashboardMetrics"] = dashboardMetrics,
                        ["chartData"] = chartData,
                        ["metricsTimeframe"] = metricsTimeframe
                    };

                    // Add old report correlation data if available
                    if (matchingOldReport != null)
                    {
                        enhancedData["originalMockData"] = new Dictionary<string, object>
                        {
                            ["originalReportId"] = matchingOldReport.ReportId,
                            ["originalClientName"] = matchingOldReport.ClientName,
                            ["originalCategory"] = matchingOldReport.Category,
                            ["originalSlideCount"] = matchingOldReport.SlideCount,
                            ["originalStatus"] = matchingOldReport.Status,
                            ["originalAuthor"] = matchingOldReport.Author
                        };
                    }

                    // Serialize enhanced data and merge with existing JsonData
                    var enhancedJsonData = JsonSerializer.Serialize(enhancedData, JsonOptions);
                    
                    // If there's existing JsonData, try to merge it
                    if (!string.IsNullOrEmpty(document.JsonData))
                    {
                        try
                        {
                            var existingData = JsonSerializer.Deserialize<Dictionary<string, object>>(document.JsonData);
                            if (existingData != null)
                            {
                                // Merge existing data with enhanced data (enhanced data takes precedence)
                                foreach (var kvp in enhancedData)
                                {
                                    existingData[kvp.Key] = kvp.Value;
                                }
                                document.JsonData = JsonSerializer.Serialize(existingData, JsonOptions);
                            }
                            else
                            {
                                document.JsonData = enhancedJsonData;
                            }
                        }
                        catch (JsonException)
                        {
                            // If existing JsonData is invalid, replace with enhanced data
                            document.JsonData = enhancedJsonData;
                        }
                    }
                    else
                    {
                        document.JsonData = enhancedJsonData;
                    }

                    // Update metadata tags to include data source information
                    document.Metadata.Tags.Add("enhanced_with_mock_data");
                    document.Metadata.Tags.Add($"metrics_{metricsTimeframe}");
                    if (matchingOldReport != null)
                    {
                        document.Metadata.Tags.Add("correlated_with_old_report");
                    }

                    _logger.LogDebug("Enhanced document for report {ReportId} with mock data correlation, timeframe: {Timeframe}, old report: {HasOldReport}",
                        report.Id, metricsTimeframe, matchingOldReport != null);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to enhance document {DocumentId} with mock data, proceeding with basic document", document.Id);
                }
            }

            // COMPREHENSIVE VALIDATION: Validate document before sending to CosmosDB
            var validationResult = ValidateDocumentForCosmosDb(document, report.Id, version.Id);
            if (!validationResult.IsValid)
            {
                _logger.LogError("Document validation failed for report {ReportId} version {VersionId}: {ValidationErrors}",
                    report.Id, version.Id, string.Join("; ", validationResult.Errors));
                throw new InvalidOperationException($"Document validation failed: {string.Join("; ", validationResult.Errors)}");
            }

            // VALIDATION: Log the exact JSON that will be sent to CosmosDB
            try
            {
                var fullJsonPreview = JsonSerializer.Serialize(document, JsonOptions);
                _logger.LogDebug("Full document JSON for report {ReportId} version {VersionId} (size: {Size} chars): {JsonPreview}",
                    report.Id, version.Id, fullJsonPreview.Length,
                    fullJsonPreview.Length > 1000 ? fullJsonPreview.Substring(0, 1000) + "..." : fullJsonPreview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to serialize document for logging - this may indicate the validation issue");
            }

            return document;
        }

        private DocumentValidationResult ValidateDocumentForCosmosDb(VersionedReportDataDocument document, Guid reportId, Guid versionId)
        {
            var result = new DocumentValidationResult();
            
            try
            {
                // Validate required fields
                if (string.IsNullOrEmpty(document.Id))
                {
                    result.AddError("Document Id is null or empty");
                }
                
                if (string.IsNullOrEmpty(document.TenantId))
                {
                    result.AddError("TenantId is null or empty");
                }
                
                if (document.ReportId == Guid.Empty)
                {
                    result.AddError("ReportId is empty GUID");
                }
                
                if (document.VersionId == Guid.Empty)
                {
                    result.AddError("VersionId is empty GUID");
                }
                
                if (document.ClientId == Guid.Empty)
                {
                    result.AddError("ClientId is empty GUID");
                }
                
                // Validate string fields for null/problematic content
                ValidateStringField(result, nameof(document.ReportName), document.ReportName);
                ValidateStringField(result, nameof(document.ReportNumber), document.ReportNumber);
                ValidateStringField(result, nameof(document.Category), document.Category);
                ValidateStringField(result, nameof(document.Status), document.Status);
                ValidateStringField(result, nameof(document.Author), document.Author);
                ValidateStringField(result, nameof(document.ClientName), document.ClientName);
                ValidateStringField(result, nameof(document.ComponentDataJson), document.ComponentDataJson);
                ValidateStringField(result, nameof(document.JsonData), document.JsonData);
                
                // Validate sections
                if (document.Sections != null)
                {
                    for (int i = 0; i < document.Sections.Count; i++)
                    {
                        var section = document.Sections[i];
                        if (section.Id == Guid.Empty)
                        {
                            result.AddError($"Section[{i}].Id is empty GUID");
                        }
                        
                        ValidateStringField(result, $"Section[{i}].Name", section.Name);
                        ValidateStringField(result, $"Section[{i}].Title", section.Title);
                        ValidateStringField(result, $"Section[{i}].SectionType", section.SectionType);
                        
                        // Validate fields within sections
                        if (section.Fields != null)
                        {
                            for (int j = 0; j < section.Fields.Count; j++)
                            {
                                var field = section.Fields[j];
                                if (field.Id == Guid.Empty)
                                {
                                    result.AddError($"Section[{i}].Field[{j}].Id is empty GUID");
                                }
                                
                                ValidateStringField(result, $"Section[{i}].Field[{j}].Name", field.Name);
                                ValidateStringField(result, $"Section[{i}].Field[{j}].Label", field.Label);
                                ValidateStringField(result, $"Section[{i}].Field[{j}].FieldType", field.FieldType);
                                ValidateStringField(result, $"Section[{i}].Field[{j}].DefaultValue", field.DefaultValue);
                            }
                        }
                    }
                }
                
                // Validate metadata
                if (document.Metadata != null)
                {
                    ValidateStringField(result, "Metadata.Version", document.Metadata.Version);
                    
                    if (document.Metadata.Tags != null)
                    {
                        for (int i = 0; i < document.Metadata.Tags.Count; i++)
                        {
                            ValidateStringField(result, $"Metadata.Tags[{i}]", document.Metadata.Tags[i]);
                        }
                    }
                }
                
                // Check document size (CosmosDB has a 2MB limit)
                try
                {
                    var serialized = JsonSerializer.Serialize(document, JsonOptions);
                    var sizeInBytes = System.Text.Encoding.UTF8.GetByteCount(serialized);
                    if (sizeInBytes > 1900000) // 1.9MB to leave some buffer
                    {
                        result.AddError($"Document size ({sizeInBytes} bytes) approaches CosmosDB limit (2MB)");
                    }
                    
                    _logger.LogDebug("Document validation passed for report {ReportId} version {VersionId}, size: {Size} bytes",
                        reportId, versionId, sizeInBytes);
                }
                catch (Exception ex)
                {
                    result.AddError($"Failed to serialize document for size check: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                result.AddError($"Validation process failed: {ex.Message}");
                _logger.LogError(ex, "Error during document validation for report {ReportId} version {VersionId}", reportId, versionId);
            }
            
            return result;
        }
        
        private void ValidateStringField(DocumentValidationResult result, string fieldName, string value)
        {
            if (value == null)
            {
                result.AddError($"{fieldName} is null");
                return;
            }
            
            // Check for problematic characters that might cause CosmosDB issues
            if (value.Contains('\0'))
            {
                result.AddError($"{fieldName} contains null character");
            }
            
            // Check for extremely long strings
            if (value.Length > 10000)
            {
                result.AddError($"{fieldName} is extremely long ({value.Length} characters)");
            }
        }

        private async Task UpdateReportVersionWithDocumentIdAsync(ApplicationDbContext context, Guid versionId, string documentId, CancellationToken cancellationToken)
        {
            var version = await context.ReportVersions
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);

            if (version != null)
            {
                version.DataDocumentId = documentId;
                version.StorageStrategy = "Hybrid"; // Updated to hybrid since we now have Cosmos data
                await context.SaveChangesAsync(cancellationToken);
            }
        }

        private async Task UpdateStorageMetadataAsync(ApplicationDbContext context, Guid reportId, VersionedReportDataDocument document, CancellationToken cancellationToken)
        {
            var metadata = await context.ReportStorageMetadata
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(rsm => rsm.ReportId == reportId, cancellationToken);

            if (metadata != null)
            {
                // Calculate document size (rough estimate)
                var documentSize = System.Text.Json.JsonSerializer.Serialize(document).Length;

                metadata.CosmosStorageSize += documentSize;
                metadata.TotalStorageSize = metadata.SqlStorageSize + metadata.CosmosStorageSize + metadata.BlobStorageSize;
                metadata.StorageStrategy = "Hybrid";
                metadata.MigrationStatus = "InProgress";
                metadata.LastModificationTime = DateTime.UtcNow;

                await context.SaveChangesAsync(cancellationToken);
            }
        }

        private async Task<OldMockData?> LoadOldMockDataAsync()
        {
            try
            {
                // Load from the new consolidated seed data file
                var seedDataPath = Path.Combine("SeedData", "CosmosDBSeedData.json");
                
                // Try multiple possible paths relative to the current working directory
                var possiblePaths = new[]
                {
                    seedDataPath,
                    Path.Combine("FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath),
                    Path.Combine("..", "FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath),
                    Path.Combine(Directory.GetCurrentDirectory(), "FY.WB.CSHero2.Infrastructure", "Persistence", seedDataPath)
                };

                string? foundPath = null;
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        foundPath = path;
                        break;
                    }
                }

                if (foundPath == null)
                {
                    _logger.LogWarning("CosmosDB seed data file not found. Tried paths: {Paths}", string.Join(", ", possiblePaths));
                    return null;
                }

                var jsonContent = await File.ReadAllTextAsync(foundPath);
                var mockData = JsonSerializer.Deserialize<OldMockData>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                _logger.LogInformation("Successfully loaded CosmosDB seed data from {FilePath}", foundPath);
                return mockData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading CosmosDB seed data");
                return null;
            }
        }

        private Dictionary<string, List<OldMockReport>> CreateCategoryMapping(List<OldMockReport> oldReports)
        {
            var mapping = new Dictionary<string, List<OldMockReport>>();

            // Group old reports by their categories and map to new categories
            var categoryMap = new Dictionary<string, string>
            {
                ["Satisfaction Survey"] = "Feedback",
                ["Performance Metrics"] = "Performance", 
                ["Action Plan"] = "Strategy",
                ["Channel Analysis"] = "Usage",
                ["User Experience"] = "Usage",
                ["Product Improvement"] = "Performance"
            };

            foreach (var oldReport in oldReports)
            {
                if (categoryMap.TryGetValue(oldReport.Category, out var newCategory))
                {
                    if (!mapping.ContainsKey(newCategory))
                        mapping[newCategory] = new List<OldMockReport>();
                    
                    mapping[newCategory].Add(oldReport);
                }
            }

            return mapping;
        }

        private OldMockReport? FindMatchingOldReport(Report report, Dictionary<string, List<OldMockReport>> categoryMapping)
        {
            if (!categoryMapping.TryGetValue(report.Category, out var oldReports) || !oldReports.Any())
                return null;

            // Use a simple round-robin approach to distribute old reports across new ones
            var index = Math.Abs(report.Id.GetHashCode()) % oldReports.Count;
            return oldReports[index];
        }

        private string DetermineMetricsTimeframe(string category)
        {
            // Map categories to appropriate timeframes
            return category switch
            {
                "Feedback" => "today",
                "Performance" => "wtd",
                "Strategy" => "mtd", 
                "Usage" => "ytd",
                _ => "today"
            };
        }

        private object GetMetricsByTimeframe(OldMockData oldMockData, string timeframe)
        {
            return timeframe switch
            {
                "today" => oldMockData.Today,
                "wtd" => oldMockData.Wtd,
                "mtd" => oldMockData.Mtd,
                "ytd" => oldMockData.Ytd,
                _ => oldMockData.Today
            };
        }

        private Dictionary<string, object> GenerateSampleChartData(string category)
        {
            var random = new Random();
            
            return new Dictionary<string, object>
            {
                ["chartType"] = category switch
                {
                    "Feedback" => "pie",
                    "Performance" => "line",
                    "Strategy" => "bar",
                    "Usage" => "area",
                    _ => "line"
                },
                ["dataPoints"] = Enumerable.Range(1, 7).Select(i => new
                {
                    label = $"Day {i}",
                    value = random.Next(10, 100)
                }).ToArray(),
                ["colors"] = category switch
                {
                    "Feedback" => new[] { "#10b981", "#3b82f6", "#f59e0b" },
                    "Performance" => new[] { "#3b82f6", "#1d4ed8" },
                    "Strategy" => new[] { "#f59e0b", "#d97706" },
                    "Usage" => new[] { "#8b5cf6", "#7c3aed" },
                    _ => new[] { "#6b7280", "#4b5563" }
                }
            };
        }

        // Document models for Cosmos DB
        public class ReportDataDocument
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
            
            [JsonPropertyName("TenantId")]
            public string TenantId { get; set; } = string.Empty; // Changed to string for partition key
            
            [JsonPropertyName("reportId")]
            public Guid ReportId { get; set; }
            
            [JsonPropertyName("versionId")]
            public Guid VersionId { get; set; }
            
            [JsonPropertyName("reportName")]
            public string ReportName { get; set; } = string.Empty;
            
            [JsonPropertyName("reportNumber")]
            public string ReportNumber { get; set; } = string.Empty;
            
            [JsonPropertyName("category")]
            public string Category { get; set; } = string.Empty;
            
            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;
            
            [JsonPropertyName("author")]
            public string Author { get; set; } = string.Empty;
            
            [JsonPropertyName("clientId")]
            public Guid ClientId { get; set; }
            
            [JsonPropertyName("clientName")]
            public string ClientName { get; set; } = string.Empty;
            
            [JsonPropertyName("sections")]
            public List<ReportDataSection> Sections { get; set; } = new();
            
            [JsonPropertyName("metadata")]
            public ReportDataMetadata Metadata { get; set; } = new();
        }

        public class VersionedReportDataDocument
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;

            [JsonPropertyName("TenantId")]
            public string TenantId { get; set; } = string.Empty;

            [JsonPropertyName("reportId")]
            public Guid ReportId { get; set; }

            [JsonPropertyName("versionId")]
            public Guid VersionId { get; set; }

            [JsonPropertyName("versionNumber")]
            public int VersionNumber { get; set; }

            [JsonPropertyName("isDraft")]
            public bool IsDraft { get; set; }

            [JsonPropertyName("reportName")]
            public string ReportName { get; set; } = string.Empty;

            [JsonPropertyName("reportNumber")]
            public string ReportNumber { get; set; } = string.Empty;

            [JsonPropertyName("category")]
            public string Category { get; set; } = string.Empty;

            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;

            [JsonPropertyName("author")]
            public string Author { get; set; } = string.Empty;

            [JsonPropertyName("clientId")]
            public Guid ClientId { get; set; }

            [JsonPropertyName("clientName")]
            public string ClientName { get; set; } = string.Empty;

            [JsonPropertyName("sections")]
            public List<ReportDataSection> Sections { get; set; } = new();

            [JsonPropertyName("componentDataJson")]
            public string ComponentDataJson { get; set; } = string.Empty;

            [JsonPropertyName("jsonData")]
            public string JsonData { get; set; } = string.Empty;

            [JsonPropertyName("metadata")]
            public ReportDataMetadata Metadata { get; set; } = new();
        }

        public class ReportDataSection
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Title { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string SectionType { get; set; } = string.Empty;
            public int DisplayOrder { get; set; }
            public bool IsRequired { get; set; }
            public List<ReportDataField> Fields { get; set; } = new();
        }

        public class ReportDataField
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Label { get; set; } = string.Empty;
            public string FieldType { get; set; } = string.Empty;
            public string DefaultValue { get; set; } = string.Empty;
            public bool IsRequired { get; set; }
            public int DisplayOrder { get; set; }
            public Dictionary<string, object> ValidationRules { get; set; } = new();
            public List<string> Options { get; set; } = new();
        }

        public class ReportDataMetadata
        {
            public DateTime CreatedAt { get; set; }
            public DateTime UpdatedAt { get; set; }
            public int SectionCount { get; set; }
            public int FieldCount { get; set; }
            public string Version { get; set; } = string.Empty;
            public List<string> Tags { get; set; } = new();
        }

        // Helper class for Cosmos DB query results
        public class DocumentIdResult
        {
            [JsonPropertyName("id")]
            public string Id { get; set; } = string.Empty;
            
            [JsonPropertyName("TenantId")]
            public string TenantId { get; set; } = string.Empty;
        }

        // Helper class for document validation
        public class DocumentValidationResult
        {
            public List<string> Errors { get; } = new();
            public bool IsValid => !Errors.Any();
            
            public void AddError(string error)
            {
                Errors.Add(error);
            }
        }

        // Data transfer objects for seeding (extracted from BlobStorageSeeder)
        public class SeededReportInfo
        {
            public Guid ReportId { get; set; }
            public string ReportNumber { get; set; } = string.Empty;
            public string ReportName { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public Guid TenantId { get; set; }
            public Guid ClientId { get; set; }
            public string ClientName { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Author { get; set; } = string.Empty;
            public int SlideCount { get; set; }
            public DateTime CreationTime { get; set; }
        }

        public class SeededTenantInfo
        {
            public Guid TenantId { get; set; }
            public string CompanyName { get; set; } = string.Empty;
            public string ContactName { get; set; } = string.Empty;
        }

        // Old mock data models (extracted from BlobStorageSeeder)
        public class OldMockData
        {
            public MetricsPeriod Today { get; set; } = new();
            public MetricsPeriod Wtd { get; set; } = new();
            public MetricsPeriod Mtd { get; set; } = new();
            public MetricsPeriod Ytd { get; set; } = new();
            public List<OldMockReport> Reports { get; set; } = new();
        }

        public class MetricsPeriod
        {
            public MetricsData Metrics { get; set; } = new();
        }

        public class MetricsData
        {
            public MetricValue TotalCustomers { get; set; } = new();
            public MetricValue NewCustomers { get; set; } = new();
            public MetricValue ReportsCreated { get; set; } = new();
            public MetricValue Revenue { get; set; } = new();
        }

        public class MetricValue
        {
            public int Current { get; set; }
            public int PreviousPeriod { get; set; }
            public List<int> Trend { get; set; } = new();
        }

        public class OldMockReport
        {
            public string ReportId { get; set; } = string.Empty;
            public string ClientId { get; set; } = string.Empty;
            public string ClientName { get; set; } = string.Empty;
            public string ReportName { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public int SlideCount { get; set; }
            public string CreatedAt { get; set; } = string.Empty;
            public string LastModified { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Author { get; set; } = string.Empty;
        }
    }
}
