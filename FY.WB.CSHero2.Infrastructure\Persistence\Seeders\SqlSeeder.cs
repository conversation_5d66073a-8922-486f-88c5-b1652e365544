using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Domain.Entities.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;
using System.Text.Json;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Identity;
using System.Data;

namespace FY.WB.CSHero2.Infrastructure.Persistence.Seeders
{
    public interface ISqlSeeder
    {
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        bool IsTableEmpty<T>(ApplicationDbContext context) where T : class;
    }

    public class SqlSeeder : ISqlSeeder
    {
        private readonly ILogger<SqlSeeder> _logger;
        private readonly IPasswordHasher<ApplicationUser> _passwordHasher;
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        public SqlSeeder(ILogger<SqlSeeder> logger, IPasswordHasher<ApplicationUser> passwordHasher)
        {
            _logger = logger;
            _passwordHasher = passwordHasher;
        }

        public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Starting SQL Server seeding...");

            try
            {
                // Check table status
                var tableStatus = await GetTableStatusAsync(context);
                LogTableStatus(tableStatus);

                // Seed in correct dependency order to avoid FK constraint violations
                // Phase 0: Identity Roles (no dependencies)
                await SeedAspNetRolesAsync(context, tableStatus.AspNetRolesEmpty, cancellationToken);
                
                // Phase 1: Independent entities (no foreign key dependencies)
                await SeedTenantProfilesAsync(context, tableStatus.TenantProfilesEmpty, cancellationToken);
                await SeedTemplatesAsync(context, tableStatus.TemplatesEmpty, cancellationToken);
                
                // Phase 1.5: Identity Users (depends on TenantProfiles for TenantId FK)
                await SeedAspNetUsersAsync(context, tableStatus.AspNetUsersEmpty, cancellationToken);
                await SeedAspNetUserRolesAsync(context, tableStatus.AspNetUserRolesEmpty, cancellationToken);
                
                // Phase 2: Entities that depend on TenantProfiles
                await SeedClientsAsync(context, tableStatus.ClientsEmpty, cancellationToken);
                
                // Phase 3: Reports (depend on Clients and TenantProfiles)
                await SeedReportsAsync(context, tableStatus.ReportsEmpty, cancellationToken);
                
                // Phase 4: Entities that depend on Reports
                await SeedReportVersionsAsync(context, tableStatus.ReportVersionsEmpty, cancellationToken);
                await SeedReportStorageMetadataAsync(context, tableStatus.ReportStorageMetadataEmpty, cancellationToken);
                await SeedReportStylesAsync(context, tableStatus.ReportStylesEmpty, cancellationToken);
                await SeedReportSectionsAsync(context, tableStatus.ReportSectionsEmpty, cancellationToken);

                // Phase 5: Entities that depend on ReportSections
                await SeedReportSectionFieldsAsync(context, tableStatus.ReportSectionFieldsEmpty, cancellationToken);
                
                // Note: Deprecated entities (Forms, Invoices, Uploads) are no longer seeded
                // Tables remain in schema for application use, but no seed data is provided

                _logger.LogInformation("SQL Server seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during SQL Server seeding");
                throw;
            }
        }

        public bool IsTableEmpty<T>(ApplicationDbContext context) where T : class
        {
            return !context.Set<T>().IgnoreQueryFilters().Any();
        }

        private async Task<TableStatus> GetTableStatusAsync(ApplicationDbContext context)
        {
            return new TableStatus
            {
                AspNetRolesEmpty = IsTableEmpty<IdentityRole<Guid>>(context),
                AspNetUsersEmpty = IsTableEmpty<ApplicationUser>(context),
                AspNetUserRolesEmpty = IsTableEmpty<IdentityUserRole<Guid>>(context),
                TenantProfilesEmpty = IsTableEmpty<TenantProfile>(context),
                ClientsEmpty = IsTableEmpty<Client>(context),
                ReportsEmpty = IsTableEmpty<Report>(context),
                ReportVersionsEmpty = IsTableEmpty<ReportVersion>(context),
                ReportStorageMetadataEmpty = IsTableEmpty<ReportStorageMetadata>(context),
                ReportStylesEmpty = IsTableEmpty<ReportStyle>(context),
                ReportSectionsEmpty = IsTableEmpty<ReportSection>(context),
                ReportSectionFieldsEmpty = IsTableEmpty<ReportSectionField>(context),
                TemplatesEmpty = IsTableEmpty<Template>(context)
            };
        }

        private void LogTableStatus(TableStatus status)
        {
            _logger.LogInformation("SQL Table Status: " +
                "AspNetRoles={AspNetRoles}, AspNetUsers={AspNetUsers}, AspNetUserRoles={AspNetUserRoles}, " +
                "TenantProfiles={TenantProfiles}, Clients={Clients}, Reports={Reports}, " +
                "ReportVersions={ReportVersions}, ReportStorageMetadata={ReportStorageMetadata}, " +
                "ReportStyles={ReportStyles}, ReportSections={ReportSections}, " +
                "ReportSectionFields={ReportSectionFields}, Templates={Templates}",
                status.AspNetRolesEmpty ? "Empty" : "Has Data",
                status.AspNetUsersEmpty ? "Empty" : "Has Data",
                status.AspNetUserRolesEmpty ? "Empty" : "Has Data",
                status.TenantProfilesEmpty ? "Empty" : "Has Data",
                status.ClientsEmpty ? "Empty" : "Has Data",
                status.ReportsEmpty ? "Empty" : "Has Data",
                status.ReportVersionsEmpty ? "Empty" : "Has Data",
                status.ReportStorageMetadataEmpty ? "Empty" : "Has Data",
                status.ReportStylesEmpty ? "Empty" : "Has Data",
                status.ReportSectionsEmpty ? "Empty" : "Has Data",
                status.ReportSectionFieldsEmpty ? "Empty" : "Has Data",
                status.TemplatesEmpty ? "Empty" : "Has Data");
        }

        private async Task SeedAspNetRolesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping AspNetRoles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding AspNetRoles with raw SQL...");
            var addedCount = 0;

            // Create admin and user roles
            var adminRoleId = Guid.NewGuid();
            var userRoleId = Guid.NewGuid();

            var roles = new[]
            {
                new { Id = adminRoleId, Name = "admin", NormalizedName = "ADMIN" },
                new { Id = userRoleId, Name = "user", NormalizedName = "USER" }
            };

            foreach (var role in roles)
            {
                try
                {
                    // Check if role already exists using EF LINQ (like TenantProfiles does)
                    var exists = await context.Roles.AnyAsync(r => r.NormalizedName == role.NormalizedName, cancellationToken);

                    if (!exists)
                    {
                        var sql = @"
                            INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
                            VALUES (@Id, @Name, @NormalizedName, @ConcurrencyStamp)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", role.Id),
                            new SqlParameter("@Name", role.Name),
                            new SqlParameter("@NormalizedName", role.NormalizedName),
                            new SqlParameter("@ConcurrencyStamp", Guid.NewGuid().ToString())
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted Role {RoleName} with ID {Id}", role.Name, role.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Role {RoleName}", role.Name);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding AspNetRoles: {Added} added", addedCount);
        }

        private async Task SeedAspNetUsersAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping AspNetUsers seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding AspNetUsers with raw SQL...");
            var tenantDtos = await ReadJsonData<TenantProfileSeedDto>("tenant-profiles.json");
            var addedCount = 0;

            // Create admin user first
            var adminUser = new ApplicationUser
            {
                Id = Guid.NewGuid(),
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                NormalizedUserName = "<EMAIL>",
                NormalizedEmail = "<EMAIL>",
                EmailConfirmed = true,
                IsAdmin = true,
                CreationTime = DateTime.UtcNow
            };

            // Check if admin user already exists using EF LINQ (like TenantProfiles does)
            var adminExists = await context.Users.AnyAsync(u => u.NormalizedEmail == adminUser.NormalizedEmail, cancellationToken);

            if (!adminExists)
            {
                var adminPasswordHash = _passwordHasher.HashPassword(adminUser, "AdminPass123!");

                try
                {
                    var sql = @"
                        INSERT INTO AspNetUsers (
                            Id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed,
                            PasswordHash, SecurityStamp, ConcurrencyStamp, PhoneNumber, PhoneNumberConfirmed,
                            TwoFactorEnabled, LockoutEnabled, AccessFailedCount, CompanyName, CompanyUrl,
                            TenantId, IsDeleted, CreationTime, CreatorId, LastModificationTime, 
                            LastModifierId, DeletionTime, DeleterId, IsAdmin, LockoutEnd
                        )
                        VALUES (
                            @Id, @UserName, @NormalizedUserName, @Email, @NormalizedEmail, @EmailConfirmed,
                            @PasswordHash, @SecurityStamp, @ConcurrencyStamp, @PhoneNumber, @PhoneNumberConfirmed,
                            @TwoFactorEnabled, @LockoutEnabled, @AccessFailedCount, @CompanyName, @CompanyUrl,
                            @TenantId, @IsDeleted, @CreationTime, @CreatorId, @LastModificationTime,
                            @LastModifierId, @DeletionTime, @DeleterId, @IsAdmin, @LockoutEnd
                        )";

                    var parameters = new[]
                    {
                        new SqlParameter("@Id", adminUser.Id),
                        new SqlParameter("@UserName", adminUser.UserName),
                        new SqlParameter("@NormalizedUserName", adminUser.NormalizedUserName),
                        new SqlParameter("@Email", adminUser.Email),
                        new SqlParameter("@NormalizedEmail", adminUser.NormalizedEmail),
                        new SqlParameter("@EmailConfirmed", adminUser.EmailConfirmed),
                        new SqlParameter("@PasswordHash", adminPasswordHash),
                        new SqlParameter("@SecurityStamp", Guid.NewGuid().ToString()),
                        new SqlParameter("@ConcurrencyStamp", Guid.NewGuid().ToString()),
                        new SqlParameter("@PhoneNumber", DBNull.Value),
                        new SqlParameter("@PhoneNumberConfirmed", false),
                        new SqlParameter("@TwoFactorEnabled", false),
                        new SqlParameter("@LockoutEnabled", true),
                        new SqlParameter("@AccessFailedCount", SqlDbType.Int) { Value = 0 },
                        new SqlParameter("@CompanyName", "System Administration"),
                        new SqlParameter("@CompanyUrl", DBNull.Value),
                        new SqlParameter("@TenantId", DBNull.Value),
                        new SqlParameter("@IsDeleted", false),
                        new SqlParameter("@CreationTime", adminUser.CreationTime),
                        new SqlParameter("@CreatorId", DBNull.Value),
                        new SqlParameter("@LastModificationTime", DBNull.Value),
                        new SqlParameter("@LastModifierId", DBNull.Value),
                        new SqlParameter("@DeletionTime", DBNull.Value),
                        new SqlParameter("@DeleterId", DBNull.Value),
                        new SqlParameter("@IsAdmin", adminUser.IsAdmin),
                        new SqlParameter("@LockoutEnd", DBNull.Value)
                    };

                    await context.Database.ExecuteSqlRawAsync(sql, parameters);
                    addedCount++;
                    _logger.LogDebug("Successfully inserted Admin User with ID {Id}", adminUser.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Admin User");
                    throw;
                }
            }

            // Create tenant users
            foreach (var dto in tenantDtos)
            {
                try
                {
                    // Check if tenant user already exists using EF LINQ (like TenantProfiles does)
                    var tenantExists = await context.Users.AnyAsync(u => u.NormalizedEmail == dto.Email.ToUpper(), cancellationToken);

                    if (!tenantExists)
                    {
                        var tenantUser = new ApplicationUser
                        {
                            Id = Guid.NewGuid(),
                            UserName = dto.Email,
                            Email = dto.Email,
                            NormalizedUserName = dto.Email.ToUpper(),
                            NormalizedEmail = dto.Email.ToUpper(),
                            EmailConfirmed = true,
                            PhoneNumber = dto.Phone,
                            CompanyName = dto.Company,
                            TenantId = dto.TenantId,
                            IsAdmin = false,
                            CreationTime = DateTime.UtcNow
                        };

                        var tenantPasswordHash = _passwordHasher.HashPassword(tenantUser, "Test123!");

                        var tenantSql = @"
                            INSERT INTO AspNetUsers (
                                Id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed,
                                PasswordHash, SecurityStamp, ConcurrencyStamp, PhoneNumber, PhoneNumberConfirmed,
                                TwoFactorEnabled, LockoutEnabled, AccessFailedCount, CompanyName, CompanyUrl,
                                TenantId, IsDeleted, CreationTime, CreatorId, LastModificationTime, 
                                LastModifierId, DeletionTime, DeleterId, IsAdmin, LockoutEnd
                            )
                            VALUES (
                                @Id, @UserName, @NormalizedUserName, @Email, @NormalizedEmail, @EmailConfirmed,
                                @PasswordHash, @SecurityStamp, @ConcurrencyStamp, @PhoneNumber, @PhoneNumberConfirmed,
                                @TwoFactorEnabled, @LockoutEnabled, @AccessFailedCount, @CompanyName, @CompanyUrl,
                                @TenantId, @IsDeleted, @CreationTime, @CreatorId, @LastModificationTime,
                                @LastModifierId, @DeletionTime, @DeleterId, @IsAdmin, @LockoutEnd
                            )";

                        var tenantParameters = new[]
                        {
                            new SqlParameter("@Id", tenantUser.Id),
                            new SqlParameter("@UserName", tenantUser.UserName),
                            new SqlParameter("@NormalizedUserName", tenantUser.NormalizedUserName),
                            new SqlParameter("@Email", tenantUser.Email),
                            new SqlParameter("@NormalizedEmail", tenantUser.NormalizedEmail),
                            new SqlParameter("@EmailConfirmed", tenantUser.EmailConfirmed),
                            new SqlParameter("@PasswordHash", tenantPasswordHash),
                            new SqlParameter("@SecurityStamp", Guid.NewGuid().ToString()),
                            new SqlParameter("@ConcurrencyStamp", Guid.NewGuid().ToString()),
                            new SqlParameter("@PhoneNumber", (object?)tenantUser.PhoneNumber ?? DBNull.Value),
                            new SqlParameter("@PhoneNumberConfirmed", false),
                            new SqlParameter("@TwoFactorEnabled", false),
                            new SqlParameter("@LockoutEnabled", true),
                            new SqlParameter("@AccessFailedCount", SqlDbType.Int) { Value = 0 },
                            new SqlParameter("@CompanyName", (object?)tenantUser.CompanyName ?? DBNull.Value),
                            new SqlParameter("@CompanyUrl", DBNull.Value),
                            new SqlParameter("@TenantId", (object?)tenantUser.TenantId ?? DBNull.Value),
                            new SqlParameter("@IsDeleted", false),
                            new SqlParameter("@CreationTime", tenantUser.CreationTime),
                            new SqlParameter("@CreatorId", DBNull.Value),
                            new SqlParameter("@LastModificationTime", DBNull.Value),
                            new SqlParameter("@LastModifierId", DBNull.Value),
                            new SqlParameter("@DeletionTime", DBNull.Value),
                            new SqlParameter("@DeleterId", DBNull.Value),
                            new SqlParameter("@IsAdmin", tenantUser.IsAdmin),
                            new SqlParameter("@LockoutEnd", DBNull.Value)
                        };

                        await context.Database.ExecuteSqlRawAsync(tenantSql, tenantParameters);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted Tenant User {Email} with ID {Id}", tenantUser.Email, tenantUser.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Tenant User {Email}", dto.Email);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding AspNetUsers: {Added} added", addedCount);
        }

        private async Task SeedAspNetUserRolesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping AspNetUserRoles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding AspNetUserRoles with raw SQL...");
            var addedCount = 0;

            try
            {
                var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.NormalizedName == "ADMIN", cancellationToken);
                var userRole = await context.Roles.FirstOrDefaultAsync(r => r.NormalizedName == "USER", cancellationToken);
                var adminUser = await context.Users.FirstOrDefaultAsync(u => u.NormalizedEmail == "<EMAIL>", cancellationToken);
                var tenantUsers = await context.Users.Where(u => !u.IsAdmin).ToListAsync(cancellationToken);

                if (adminRole != null && adminUser != null)
                {
                    // Assign admin role to admin user
                    var adminUserRoleSql = @"
                        INSERT INTO AspNetUserRoles (UserId, RoleId)
                        VALUES (@UserId, @RoleId)";

                    var adminUserRoleParams = new[]
                    {
                        new SqlParameter("@UserId", adminUser.Id),
                        new SqlParameter("@RoleId", adminRole.Id)
                    };

                    await context.Database.ExecuteSqlRawAsync(adminUserRoleSql, adminUserRoleParams);
                    addedCount++;
                    _logger.LogDebug("Successfully assigned admin role to admin user");
                }

                if (userRole != null)
                {
                    // Assign user role to all tenant users
                    foreach (var tenantUser in tenantUsers)
                    {
                        var tenantUserRoleSql = @"
                            INSERT INTO AspNetUserRoles (UserId, RoleId)
                            VALUES (@UserId, @RoleId)";

                        var tenantUserRoleParams = new[]
                        {
                            new SqlParameter("@UserId", tenantUser.Id),
                            new SqlParameter("@RoleId", userRole.Id)
                        };

                        await context.Database.ExecuteSqlRawAsync(tenantUserRoleSql, tenantUserRoleParams);
                        addedCount++;
                        _logger.LogDebug("Successfully assigned user role to tenant user {Email}", tenantUser.Email);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to seed AspNetUserRoles");
                throw;
            }

            _logger.LogInformation("Completed seeding AspNetUserRoles: {Added} added", addedCount);
        }

        private async Task SeedTenantProfilesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping TenantProfiles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding TenantProfiles with raw SQL...");
            var dtos = await ReadJsonData<TenantProfileSeedDto>("tenant-profiles.json");
            var addedCount = 0;

            foreach (var dto in dtos)
            {
                try
                {
                    // Check if entity already exists using EF LINQ
                    var exists = await context.TenantProfiles.AnyAsync(tp => tp.Id == dto.Id, cancellationToken);

                    if (!exists)
                    {
                        var paymentMethod = dto.PaymentMethod != null
                            ? JsonSerializer.Serialize(dto.PaymentMethod, JsonOptions)
                            : "{}";
                        var billingAddress = dto.BillingAddress != null
                            ? JsonSerializer.Serialize(dto.BillingAddress, JsonOptions)
                            : "{}";

                        var sql = @"
                            INSERT INTO TenantProfiles (Id, Name, Email, Status, Phone, Company, Subscription,
                                                      LastLoginTime, BillingCycle, NextBillingDate, SubscriptionStatus,
                                                      PaymentMethod, BillingAddress, TenantId, CreationTime, LastModificationTime, IsDeleted)
                            VALUES (@Id, @Name, @Email, @Status, @Phone, @Company, @Subscription,
                                   @LastLoginTime, @BillingCycle, @NextBillingDate, @SubscriptionStatus,
                                   @PaymentMethod, @BillingAddress, @TenantId, @CreationTime, @LastModificationTime, @IsDeleted)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", dto.Id),
                            new SqlParameter("@Name", dto.Name),
                            new SqlParameter("@Email", dto.Email),
                            new SqlParameter("@Status", dto.Status),
                            new SqlParameter("@Phone", dto.Phone),
                            new SqlParameter("@Company", dto.Company),
                            new SqlParameter("@Subscription", dto.Subscription),
                            new SqlParameter("@LastLoginTime", dto.LastLoginTime),
                            new SqlParameter("@BillingCycle", dto.BillingCycle),
                            new SqlParameter("@NextBillingDate", dto.NextBillingDate),
                            new SqlParameter("@SubscriptionStatus", dto.SubscriptionStatus),
                            new SqlParameter("@PaymentMethod", paymentMethod),
                            new SqlParameter("@BillingAddress", billingAddress),
                            new SqlParameter("@TenantId", dto.Id),
                            new SqlParameter("@CreationTime", DateTime.UtcNow),
                            new SqlParameter("@LastModificationTime", DateTime.UtcNow),
                            new SqlParameter("@IsDeleted", false)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted TenantProfile with ID {Id}", dto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert TenantProfile with ID {Id}", dto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding TenantProfile: {Added} added", addedCount);
        }

        private async Task SeedClientsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Clients seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Clients with raw SQL...");
            var dtos = await ReadJsonData<ClientSeedDto>("clients.json");
            var addedCount = 0;

            foreach (var dto in dtos)
            {
                try
                {
                    // Check if entity already exists using EF LINQ
                    var exists = await context.Clients.AnyAsync(c => c.Id == dto.Id, cancellationToken);

                    if (!exists)
                    {
                        var sql = @"
                            INSERT INTO Clients (Id, Name, Email, Status, CompanyName, Phone, Address,
                                               CompanySize, Industry, TenantId, CreationTime, LastModificationTime, IsDeleted)
                            VALUES (@Id, @Name, @Email, @Status, @CompanyName, @Phone, @Address,
                                   @CompanySize, @Industry, @TenantId, @CreationTime, @LastModificationTime, @IsDeleted)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", dto.Id),
                            new SqlParameter("@Name", dto.Name),
                            new SqlParameter("@Email", dto.Email),
                            new SqlParameter("@Status", dto.Status),
                            new SqlParameter("@CompanyName", dto.CompanyName),
                            new SqlParameter("@Phone", (object?)dto.Phone ?? DBNull.Value),
                            new SqlParameter("@Address", (object?)dto.Address ?? DBNull.Value),
                            new SqlParameter("@CompanySize", (object?)dto.CompanySize ?? DBNull.Value),
                            new SqlParameter("@Industry", (object?)dto.Industry ?? DBNull.Value),
                            new SqlParameter("@TenantId", (object?)dto.TenantId ?? DBNull.Value),
                            new SqlParameter("@CreationTime", dto.CreatedAt == default ? DateTime.UtcNow : dto.CreatedAt),
                            new SqlParameter("@LastModificationTime", dto.UpdatedAt == default ? DateTime.UtcNow : dto.UpdatedAt),
                            new SqlParameter("@IsDeleted", false)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted Client with ID {Id}", dto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Client with ID {Id}", dto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding Client: {Added} added", addedCount);
        }

        private async Task SeedReportsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Reports seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Reports with raw SQL and FK validation...");
            var reportDtos = await ReadJsonData<ReportSeedDto>("reports.json");
            var allClients = await context.Clients.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var addedCount = 0;
            var skippedCount = 0;

            foreach (var reportDto in reportDtos)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.Reports.AnyAsync(r => r.Id == reportDto.Id, cancellationToken);

                    if (!exists)
                    {
                        var client = allClients.FirstOrDefault(c =>
                            c.CompanyName == reportDto.ClientName &&
                            c.TenantId == reportDto.TenantId);

                        if (client == null)
                        {
                            _logger.LogWarning("Skipping Report '{ReportName}' - Client '{ClientName}' not found for TenantId {TenantId}",
                                reportDto.Name, reportDto.ClientName, reportDto.TenantId);
                            skippedCount++;
                            continue;
                        }

                        var sql = @"
                            INSERT INTO Reports (Id, ReportNumber, ClientId, ClientName, Name, Category,
                                               SlideCount, Status, Author, TenantId, CreationTime,
                                               LastModificationTime, IsDeleted, IsDraft, LastSavedAt, ReportType,
                                               TemplateId, CurrentVersionId, DataDocumentId, ComponentsBlobId, DraftDataDocumentId)
                            VALUES (@Id, @ReportNumber, @ClientId, @ClientName, @Name, @Category,
                                   @SlideCount, @Status, @Author, @TenantId, @CreationTime,
                                   @LastModificationTime, @IsDeleted, @IsDraft, @LastSavedAt, @ReportType,
                                   @TemplateId, @CurrentVersionId, @DataDocumentId, @ComponentsBlobId, @DraftDataDocumentId)";

                        var creationTime = reportDto.CreationTime == default ? DateTime.UtcNow : reportDto.CreationTime;
                        var parameters = new[]
                        {
                            new SqlParameter("@Id", reportDto.Id),
                            new SqlParameter("@ReportNumber", reportDto.ReportNumber),
                            new SqlParameter("@ClientId", client.Id),
                            new SqlParameter("@ClientName", reportDto.ClientName),
                            new SqlParameter("@Name", reportDto.Name),
                            new SqlParameter("@Category", reportDto.Category),
                            new SqlParameter("@SlideCount", reportDto.SlideCount),
                            new SqlParameter("@Status", reportDto.Status),
                            new SqlParameter("@Author", reportDto.Author),
                            new SqlParameter("@TenantId", (object?)reportDto.TenantId ?? DBNull.Value),
                            new SqlParameter("@CreationTime", creationTime),
                            new SqlParameter("@LastModificationTime", creationTime),
                            new SqlParameter("@IsDeleted", reportDto.IsDeleted),
                            new SqlParameter("@IsDraft", false), // Seed data represents saved reports
                            new SqlParameter("@LastSavedAt", creationTime),
                            new SqlParameter("@ReportType", "Standard"),
                            new SqlParameter("@TemplateId", DBNull.Value),
                            new SqlParameter("@CurrentVersionId", DBNull.Value),
                            new SqlParameter("@DataDocumentId", DBNull.Value),
                            new SqlParameter("@ComponentsBlobId", DBNull.Value),
                            new SqlParameter("@DraftDataDocumentId", DBNull.Value)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted Report with ID {Id}", reportDto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Report with ID {Id}", reportDto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding Report: {Added} added, {Skipped} skipped", addedCount, skippedCount);
        }

        private async Task SeedReportVersionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportVersions seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportVersions with raw SQL and FK validation...");
            var versionDtos = await ReadJsonData<ReportVersionSeedDto>("report-versions.json");
            var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var addedCount = 0;
            var skippedCount = 0;

            foreach (var versionDto in versionDtos)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.ReportVersions.AnyAsync(rv => rv.Id == versionDto.Id, cancellationToken);

                    if (!exists)
                    {
                        var reportExists = allReports.Any(r => r.Id == versionDto.ReportId);

                        if (!reportExists)
                        {
                            _logger.LogWarning("Skipping ReportVersion - Report not found (SqlSeeder.cs:225)");
                            skippedCount++;
                            continue;
                        }

                        var sql = @"
                            INSERT INTO ReportVersions (Id, ReportId, VersionNumber, Description, CreatedAt,
                                                       ComponentDataJson, JsonData, IsCurrent, ComponentDataSize, JsonDataSize,
                                                       StyleDocumentId, DataBlobPath, IsDataInBlob, DataDocumentId, ComponentsBlobId,
                                                       CreationTime, LastModificationTime, StorageStrategy, StylesBlobId, StylesSize)
                            VALUES (@Id, @ReportId, @VersionNumber, @Description, @CreatedAt,
                                   @ComponentDataJson, @JsonData, @IsCurrent, @ComponentDataSize, @JsonDataSize,
                                   @StyleDocumentId, @DataBlobPath, @IsDataInBlob, @DataDocumentId, @ComponentsBlobId,
                                   @CreationTime, @LastModificationTime, @StorageStrategy, @StylesBlobId, @StylesSize)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", versionDto.Id),
                            new SqlParameter("@ReportId", versionDto.ReportId),
                            new SqlParameter("@VersionNumber", versionDto.VersionNumber),
                            new SqlParameter("@Description", versionDto.Description),
                            new SqlParameter("@CreatedAt", versionDto.CreatedAt),
                            new SqlParameter("@ComponentDataJson", versionDto.ComponentDataJson),
                            new SqlParameter("@JsonData", versionDto.JsonData),
                            new SqlParameter("@IsCurrent", versionDto.IsCurrent),
                            new SqlParameter("@ComponentDataSize", versionDto.ComponentDataSize),
                            new SqlParameter("@JsonDataSize", versionDto.JsonDataSize),
                            new SqlParameter("@StyleDocumentId", DBNull.Value),
                            new SqlParameter("@DataBlobPath", DBNull.Value),
                            new SqlParameter("@IsDataInBlob", versionDto.IsDataInBlob),
                            new SqlParameter("@DataDocumentId", DBNull.Value),
                            new SqlParameter("@ComponentsBlobId", DBNull.Value),
                            new SqlParameter("@CreationTime", versionDto.CreationTime == default ? DateTime.UtcNow : versionDto.CreationTime),
                            new SqlParameter("@LastModificationTime", versionDto.LastModificationTime == default ? DateTime.UtcNow : versionDto.LastModificationTime),
                            new SqlParameter("@StorageStrategy", versionDto.StorageStrategy),
                            new SqlParameter("@StylesBlobId", DBNull.Value),
                            new SqlParameter("@StylesSize", versionDto.StylesSize)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted ReportVersion with ID {Id}", versionDto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert ReportVersion with ID {Id}", versionDto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding ReportVersion: {Added} added, {Skipped} skipped", addedCount, skippedCount);
        }

        private async Task SeedReportStorageMetadataAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportStorageMetadata seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportStorageMetadata...");
            var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var metadataEntities = new List<ReportStorageMetadata>();

            foreach (var report in allReports)
            {
                var metadata = new ReportStorageMetadata(
                    Guid.NewGuid(),
                    report.Id,
                    "SQL",
                    "NotMigrated");

                metadata.SqlStorageSize = 1024; // Placeholder - will be calculated
                metadata.CosmosStorageSize = 0;
                metadata.BlobStorageSize = 0;
                metadata.TotalStorageSize = 1024;
                metadata.AccessCount = 0;
                metadata.CreationTime = DateTime.UtcNow;
                metadata.LastModificationTime = DateTime.UtcNow;

                metadataEntities.Add(metadata);
            }

            await SeedEntities(context, metadataEntities, "ReportStorageMetadata", cancellationToken);
        }

        private async Task SeedReportStylesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportStyles seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportStyles with raw SQL...");
            var entities = await ReadJsonData<ReportStyleSeedDto>("report-styles.json");
            var addedCount = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.ReportStyles.AnyAsync(rs => rs.Id == entity.Id, cancellationToken);

                    if (!exists)
                    {
                        var sql = @"
                            INSERT INTO ReportStyles (Id, ReportId, Theme, ColorScheme, Typography, Spacing,
                                                    LayoutOptionsJson, TypographyOptionsJson, StructureOptionsJson,
                                                    ContentOptionsJson, VisualOptionsJson, CreationTime, LastModificationTime)
                            VALUES (@Id, @ReportId, @Theme, @ColorScheme, @Typography, @Spacing,
                                   @LayoutOptionsJson, @TypographyOptionsJson, @StructureOptionsJson,
                                   @ContentOptionsJson, @VisualOptionsJson, @CreationTime, @LastModificationTime)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", entity.Id),
                            new SqlParameter("@ReportId", entity.ReportId),
                            new SqlParameter("@Theme", entity.Theme),
                            new SqlParameter("@ColorScheme", entity.ColorScheme),
                            new SqlParameter("@Typography", entity.Typography),
                            new SqlParameter("@Spacing", entity.Spacing),
                            new SqlParameter("@LayoutOptionsJson", entity.LayoutOptionsJson),
                            new SqlParameter("@TypographyOptionsJson", entity.TypographyOptionsJson),
                            new SqlParameter("@StructureOptionsJson", entity.StructureOptionsJson),
                            new SqlParameter("@ContentOptionsJson", entity.ContentOptionsJson),
                            new SqlParameter("@VisualOptionsJson", entity.VisualOptionsJson),
                            new SqlParameter("@CreationTime", DateTime.UtcNow),
                            new SqlParameter("@LastModificationTime", DateTime.UtcNow)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted ReportStyle with ID {Id}", entity.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert ReportStyle with ID {Id}", entity.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding ReportStyle: {Added} added", addedCount);
        }

        private async Task SeedReportSectionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportSections seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportSections with raw SQL and FK validation...");
            var sectionDtos = await ReadJsonData<ReportSectionSeedDto>("report-sections.json");
            var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
            var addedCount = 0;
            var skippedCount = 0;

            foreach (var sectionDto in sectionDtos)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.ReportSections.AnyAsync(rs => rs.Id == sectionDto.Id, cancellationToken);

                    if (!exists)
                    {
                        var reportExists = allReports.Any(r => r.Id == sectionDto.ReportId);

                        if (!reportExists)
                        {
                            _logger.LogWarning("Skipping ReportSection '{SectionId}' - Report '{ReportId}' not found",
                                sectionDto.Id, sectionDto.ReportId);
                            skippedCount++;
                            continue;
                        }

                        var sql = @"
                            INSERT INTO ReportSections (Id, ReportId, Title, Type, [Order], CreationTime, LastModificationTime)
                            VALUES (@Id, @ReportId, @Title, @Type, @Order, @CreationTime, @LastModificationTime)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", sectionDto.Id),
                            new SqlParameter("@ReportId", sectionDto.ReportId),
                            new SqlParameter("@Title", sectionDto.Title),
                            new SqlParameter("@Type", sectionDto.SectionType),
                            new SqlParameter("@Order", sectionDto.OrderIndex),
                            new SqlParameter("@CreationTime", DateTime.UtcNow),
                            new SqlParameter("@LastModificationTime", DateTime.UtcNow)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted ReportSection with ID {Id}", sectionDto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert ReportSection with ID {Id}", sectionDto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding ReportSection: {Added} added, {Skipped} skipped", addedCount, skippedCount);
        }

        private async Task SeedReportSectionFieldsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping ReportSectionFields seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding ReportSectionFields with raw SQL...");
            var entities = await ReadJsonData<ReportSectionFieldSeedDto>("report-section-fields.json");
            var addedCount = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.ReportSectionFields.AnyAsync(rsf => rsf.Id == entity.Id, cancellationToken);

                    if (!exists)
                    {
                        var sql = @"
                            INSERT INTO ReportSectionFields (Id, SectionId, Name, Type, Content, [Order], CreationTime, LastModificationTime)
                            VALUES (@Id, @SectionId, @Name, @Type, @Content, @Order, @CreationTime, @LastModificationTime)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", entity.Id),
                            new SqlParameter("@SectionId", entity.SectionId),
                            new SqlParameter("@Name", entity.Name),
                            new SqlParameter("@Type", entity.Type),
                            new SqlParameter("@Content", entity.Content),
                            new SqlParameter("@Order", entity.Order),
                            new SqlParameter("@CreationTime", DateTime.UtcNow),
                            new SqlParameter("@LastModificationTime", DateTime.UtcNow)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted ReportSectionField with ID {Id}", entity.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert ReportSectionField with ID {Id}", entity.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding ReportSectionField: {Added} added", addedCount);
        }


        private async Task SeedTemplatesAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
        {
            if (!isEmpty)
            {
                _logger.LogInformation("Skipping Templates seeding - table has data");
                return;
            }

            _logger.LogInformation("Seeding Templates with raw SQL...");
            var dtos = await ReadJsonData<TemplateSeedDto>("templates.json");
            var addedCount = 0;

            foreach (var dto in dtos)
            {
                try
                {
                    // Check if entity already exists
                    // Check if entity already exists using EF LINQ
                    var exists = await context.Templates.AnyAsync(t => t.Id == dto.Id, cancellationToken);

                    if (!exists)
                    {
                        var tags = dto.Tags != null ? JsonSerializer.Serialize(dto.Tags, JsonOptions) : "[]";
                        var sections = dto.Sections != null ? JsonSerializer.Serialize(dto.Sections, JsonOptions) : "[]";
                        var fields = dto.Fields != null ? JsonSerializer.Serialize(dto.Fields, JsonOptions) : "[]";

                        var sql = @"
                            INSERT INTO Templates (Id, Name, Description, Category, ThumbnailUrl, Tags, Sections, Fields,
                                                 TenantId, CreationTime, LastModificationTime, IsDeleted, IsPublic, IsActive,
                                                 UsageCount, Version, EstimatedCompletionTimeMinutes, DefaultStyleJson, StyleDocumentId)
                            VALUES (@Id, @Name, @Description, @Category, @ThumbnailUrl, @Tags, @Sections, @Fields,
                                   @TenantId, @CreationTime, @LastModificationTime, @IsDeleted, @IsPublic, @IsActive,
                                   @UsageCount, @Version, @EstimatedCompletionTimeMinutes, @DefaultStyleJson, @StyleDocumentId)";

                        var parameters = new[]
                        {
                            new SqlParameter("@Id", dto.Id),
                            new SqlParameter("@Name", dto.Name),
                            new SqlParameter("@Description", dto.Description),
                            new SqlParameter("@Category", dto.Category),
                            new SqlParameter("@ThumbnailUrl", dto.ThumbnailUrl),
                            new SqlParameter("@Tags", tags),
                            new SqlParameter("@Sections", sections),
                            new SqlParameter("@Fields", fields),
                            new SqlParameter("@TenantId", (object?)dto.TenantId ?? DBNull.Value),
                            new SqlParameter("@CreationTime", DateTime.UtcNow),
                            new SqlParameter("@LastModificationTime", DateTime.UtcNow),
                            new SqlParameter("@IsDeleted", false),
                            new SqlParameter("@IsPublic", false),
                            new SqlParameter("@IsActive", true),
                            new SqlParameter("@UsageCount", 2),
                            new SqlParameter("@Version", "1.0.0"),
                            new SqlParameter("@EstimatedCompletionTimeMinutes", 30),
                            new SqlParameter("@DefaultStyleJson", "{}"),
                            new SqlParameter("@StyleDocumentId", DBNull.Value)
                        };

                        await context.Database.ExecuteSqlRawAsync(sql, parameters, cancellationToken);
                        addedCount++;
                        _logger.LogDebug("Successfully inserted Template with ID {Id}", dto.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to insert Template with ID {Id}", dto.Id);
                    throw;
                }
            }

            _logger.LogInformation("Completed seeding Template: {Added} added", addedCount);
        }


        private async Task<List<T>> ReadJsonData<T>(string fileName)
        {
            var path = FindSeedDataFile(fileName);
            if (string.IsNullOrEmpty(path))
            {
                _logger.LogWarning("Seed data file not found: {FileName}", fileName);
                return new List<T>();
            }

            try
            {
                _logger.LogDebug("Reading seed data from: {FilePath}", path);
                var json = await File.ReadAllTextAsync(path);
                var data = JsonSerializer.Deserialize<List<T>>(json, JsonOptions);
                if (data == null || !data.Any())
                {
                    _logger.LogWarning("No data found in seed file: {FileName}", fileName);
                    return new List<T>();
                }
                _logger.LogInformation("Successfully read {Count} records from {FileName}", data.Count, fileName);
                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading seed data from {FileName} at path {FilePath}", fileName, path);
                throw;
            }
        }

        private string? FindSeedDataFile(string fileName)
        {
            // Strategy 1: Use assembly location as base
            var assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            var assemblyDir = Path.GetDirectoryName(assemblyLocation);
            
            if (!string.IsNullOrEmpty(assemblyDir))
            {
                // Look for SeedData folder relative to assembly location
                var seedDataPaths = new[]
                {
                    Path.Combine(assemblyDir, "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                    Path.Combine(assemblyDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
                };

                foreach (var seedPath in seedDataPaths)
                {
                    var fullPath = Path.GetFullPath(seedPath);
                    _logger.LogDebug("Checking seed data path: {Path}", fullPath);
                    if (File.Exists(fullPath))
                    {
                        _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                        return fullPath;
                    }
                }
            }

            // Strategy 2: Use current working directory
            var currentDir = Directory.GetCurrentDirectory();
            var workingDirPaths = new[]
            {
                Path.Combine(currentDir, "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(currentDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var workingPath in workingDirPaths)
            {
                var fullPath = Path.GetFullPath(workingPath);
                _logger.LogDebug("Checking working directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            // Strategy 3: Search from AppContext.BaseDirectory
            var baseDir = AppContext.BaseDirectory;
            var baseDirPaths = new[]
            {
                Path.Combine(baseDir, "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName),
                Path.Combine(baseDir, "..", "..", "..", "..", "FY.WB.CSHero2.Infrastructure", "Persistence", "SeedData", fileName)
            };

            foreach (var basePath in baseDirPaths)
            {
                var fullPath = Path.GetFullPath(basePath);
                _logger.LogDebug("Checking base directory path: {Path}", fullPath);
                if (File.Exists(fullPath))
                {
                    _logger.LogDebug("Found seed data file at: {Path}", fullPath);
                    return fullPath;
                }
            }

            _logger.LogError("Could not locate seed data file: {FileName}. Searched in assembly location: {AssemblyDir}, working directory: {WorkingDir}, base directory: {BaseDir}",
                fileName, assemblyDir, currentDir, baseDir);
            return null;
        }

        private async Task SeedEntities<T>(ApplicationDbContext context, List<T> entities, string entityName, CancellationToken cancellationToken) where T : class
        {
            var addedCount = 0;
            var skippedCount = 0;

            foreach (var entity in entities)
            {
                try
                {
                    // Check if entity already exists (assuming entities have Id property)
                    var idProperty = typeof(T).GetProperty("Id");
                    if (idProperty != null)
                    {
                        var id = idProperty.GetValue(entity);
                        var exists = await context.Set<T>().IgnoreQueryFilters()
                            .AnyAsync(e => EF.Property<object>(e, "Id").Equals(id), cancellationToken);

                        if (!exists)
                        {
                            // Set audit properties if entity supports them
                            SetAuditProperties(entity);
                            context.Set<T>().Add(entity);
                            addedCount++;
                        }
                        else
                        {
                            skippedCount++;
                        }
                    }
                    else
                    {
                        // Fallback for entities without Id property
                        context.Set<T>().Add(entity);
                        addedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing {EntityName} record", entityName);
                    throw;
                }
            }

            if (addedCount > 0)
            {
                await context.SaveChangesAsync(cancellationToken);
            }

            _logger.LogInformation("Completed seeding {EntityName}: {Added} added, {Skipped} skipped",
                entityName, addedCount, skippedCount);
        }

        private static void SetAuditProperties<T>(T entity)
        {
            var now = DateTime.UtcNow;

            // Set CreationTime if property exists and is default
            var creationTimeProperty = typeof(T).GetProperty("CreationTime");
            if (creationTimeProperty != null && creationTimeProperty.CanWrite)
            {
                var currentValue = creationTimeProperty.GetValue(entity);
                if (currentValue is DateTime dt && dt == default)
                {
                    creationTimeProperty.SetValue(entity, now);
                }
            }

            // Set LastModificationTime if property exists and is null
            var lastModificationTimeProperty = typeof(T).GetProperty("LastModificationTime");
            if (lastModificationTimeProperty != null && lastModificationTimeProperty.CanWrite)
            {
                var currentValue = lastModificationTimeProperty.GetValue(entity);
                if (currentValue == null)
                {
                    lastModificationTimeProperty.SetValue(entity, now);
                }
            }
        }

        // DTO classes for JSON deserialization
        public class TenantProfileSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string Phone { get; set; } = string.Empty;
            public string Company { get; set; } = string.Empty;
            public string Subscription { get; set; } = string.Empty;
            public DateTime LastLoginTime { get; set; }
            public string BillingCycle { get; set; } = string.Empty;
            public DateTime NextBillingDate { get; set; }
            public string SubscriptionStatus { get; set; } = string.Empty;
            public PaymentMethodInfo? PaymentMethod { get; set; }
            public BillingAddressInfo? BillingAddress { get; set; }
            public Guid? TenantId { get; set; }
        }

        public class TemplateSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public string ThumbnailUrl { get; set; } = string.Empty;
            public List<string>? Tags { get; set; }
            public List<TemplateSection>? Sections { get; set; }
            public List<TemplateField>? Fields { get; set; }
            public Guid? TenantId { get; set; }
        }

        public class ClientSeedDto
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public string Status { get; set; } = string.Empty;
            public string CompanyName { get; set; } = string.Empty;
            public string? Phone { get; set; }
            public string? Address { get; set; }
            public string? CompanySize { get; set; }
            public string? Industry { get; set; }
            public DateTime CreatedAt { get; set; } // Maps to CreationTime
            public DateTime UpdatedAt { get; set; } // Maps to LastModificationTime
            public Guid? TenantId { get; set; }
        }

        public class ReportSeedDto
        {
            public Guid Id { get; set; }
            public string ReportNumber { get; set; } = string.Empty;
            public Guid ClientId { get; set; }
            public string ClientName { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Category { get; set; } = string.Empty;
            public int SlideCount { get; set; }
            public string Status { get; set; } = string.Empty;
            public string Author { get; set; } = string.Empty;
            public Guid TenantId { get; set; }
            public DateTime CreationTime { get; set; }
            public bool IsDeleted { get; set; }
        }

        public class ReportStyleSeedDto
        {
            public Guid Id { get; set; }
            public Guid ReportId { get; set; }
            public string Theme { get; set; } = string.Empty;
            public string ColorScheme { get; set; } = string.Empty;
            public string Typography { get; set; } = string.Empty;
            public string Spacing { get; set; } = string.Empty;
            public string LayoutOptionsJson { get; set; } = string.Empty;
            public string TypographyOptionsJson { get; set; } = string.Empty;
            public string StructureOptionsJson { get; set; } = string.Empty;
            public string ContentOptionsJson { get; set; } = string.Empty;
            public string VisualOptionsJson { get; set; } = string.Empty;
            public DateTime CreationTime { get; set; }
            public DateTime LastModificationTime { get; set; }
        }

        public class ReportSectionSeedDto
        {
            public Guid Id { get; set; }
            public Guid ReportId { get; set; }
            public string SectionType { get; set; } = string.Empty;
            public string Title { get; set; } = string.Empty;
            public string Content { get; set; } = string.Empty;
            public int OrderIndex { get; set; }
            public string ConfigurationJson { get; set; } = string.Empty;
            public DateTime CreationTime { get; set; }
            public DateTime LastModificationTime { get; set; }
        }

        public class ReportSectionFieldSeedDto
        {
            public Guid Id { get; set; }
            public Guid SectionId { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public string Content { get; set; } = string.Empty;
            public int Order { get; set; }
            public bool IsRequired { get; set; }
            public string ConfigurationJson { get; set; } = string.Empty;
            public DateTime CreationTime { get; set; }
            public DateTime LastModificationTime { get; set; }
        }

        public class ReportVersionSeedDto
        {
            public Guid Id { get; set; }
            public Guid ReportId { get; set; }
            public int VersionNumber { get; set; }
            public string Description { get; set; } = string.Empty;
            public DateTime CreatedAt { get; set; }
            public string ComponentDataJson { get; set; } = string.Empty;
            public string JsonData { get; set; } = string.Empty;
            public bool IsCurrent { get; set; }
            public long ComponentDataSize { get; set; }
            public long JsonDataSize { get; set; }
            public bool IsDataInBlob { get; set; }
            public DateTime CreationTime { get; set; }
            public string StorageStrategy { get; set; } = string.Empty;
            public long StylesSize { get; set; }
            public string ReportData { get; set; } = string.Empty;
            public string ComponentData { get; set; } = string.Empty;
            public Guid CreatedBy { get; set; }
            public DateTime LastModificationTime { get; set; }
        }

        private class TableStatus
        {
            public bool AspNetRolesEmpty { get; set; }
            public bool AspNetUsersEmpty { get; set; }
            public bool AspNetUserRolesEmpty { get; set; }
            public bool TenantProfilesEmpty { get; set; }
            public bool ClientsEmpty { get; set; }
            public bool ReportsEmpty { get; set; }
            public bool ReportVersionsEmpty { get; set; }
            public bool ReportStorageMetadataEmpty { get; set; }
            public bool ReportStylesEmpty { get; set; }
            public bool ReportSectionsEmpty { get; set; }
            public bool ReportSectionFieldsEmpty { get; set; }
            public bool TemplatesEmpty { get; set; }
        }
    }
}
