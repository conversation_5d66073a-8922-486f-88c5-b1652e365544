using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Implementation of component generator for creating React components from report data
    /// </summary>
    public class ComponentGeneratorImpl : IComponentGenerator
    {
        private readonly ILogger<ComponentGeneratorImpl> _logger;
        private readonly ComponentBuilder _componentBuilder;
        private readonly ComponentGenerationWorkflow _workflow;

        public ComponentGeneratorImpl(
            ILogger<ComponentGeneratorImpl> logger,
            ComponentBuilder componentBuilder,
            ComponentGenerationWorkflow workflow)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _componentBuilder = componentBuilder ?? throw new ArgumentNullException(nameof(componentBuilder));
            _workflow = workflow ?? throw new ArgumentNullException(nameof(workflow));
        }

        /// <summary>
        /// Generates React components for an entire report
        /// </summary>
        public async Task<ComponentResult> GenerateComponentAsync(ComponentRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Generating components for report {ReportId}", request.ReportId);

                // Use the ComponentBuilder to build the components
                var result = await _componentBuilder.BuildComponentsAsync(request, cancellationToken);

                _logger.LogInformation("Successfully generated {ComponentCount} components for report {ReportId}",
                    result.Components.Count, request.ReportId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating components for report {ReportId}", request.ReportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Component generation failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Generates a React component for a specific section using enhanced workflow
        /// </summary>
        public async Task<ComponentResult> GenerateSectionComponentAsync(SectionComponentRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Generating component for section {SectionId} in report {ReportId} using enhanced workflow",
                    request.SectionId, request.ReportId);

                // Create component generation context
                var context = new ComponentGenerationContext
                {
                    SectionId = request.SectionId,
                    SectionName = request.SectionName,
                    Framework = request.Options.Framework,
                    StyleFramework = request.Options.StyleFramework,
                    UseTypeScript = request.Options.UseTypeScript,
                    IncludeAccessibility = request.Options.IncludeAccessibility,
                    IncludeResponsiveDesign = request.Options.IncludeResponsiveDesign,
                    OptimizeForPerformance = request.Options.OptimizeForPerformance,
                    Data = request.Data,
                    ComponentType = "section"
                };

                // Create workflow request
                var workflowRequest = new ComponentGenerationRequest
                {
                    ReportId = request.ReportId,
                    Context = context,
                    Data = request.Data,
                    ValidateComponent = true,
                    RequireValidComponent = false,
                    GenerateTypeDefinitions = request.Options.UseTypeScript
                };

                // Execute the enhanced workflow
                var workflowResult = await _workflow.GenerateComponentAsync(workflowRequest, cancellationToken);

                // Convert workflow result to ComponentResult
                var result = new ComponentResult
                {
                    Success = workflowResult.Success,
                    Metadata = new ComponentMetadata
                    {
                        Framework = request.Options.Framework,
                        StyleFramework = request.Options.StyleFramework,
                        GeneratedAt = DateTime.UtcNow
                    },
                    Errors = workflowResult.Errors,
                    Warnings = workflowResult.Warnings
                };

                if (workflowResult.Success)
                {
                    var component = new SectionComponent
                    {
                        SectionId = request.SectionId,
                        SectionName = request.SectionName,
                        ComponentCode = workflowResult.ComponentCode,
                        TypeDefinitions = workflowResult.TypeDefinitions
                    };

                    result.Components.Add(component);
                }

                _logger.LogInformation("Enhanced component generation completed for section {SectionId} in {Duration}ms (Cache Hit: {CacheHit})",
                    request.SectionId, workflowResult.Duration.TotalMilliseconds, workflowResult.CacheHit);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in enhanced component generation for section {SectionId} in report {ReportId}",
                    request.SectionId, request.ReportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Enhanced section component generation failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Generates TypeScript type definitions for a component
        /// </summary>
        public async Task<string> GenerateTypeDefinitionsAsync(string componentCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating type definitions for component code");

                var typeBuilder = new StringBuilder();
                // Extract component name from the code (simplified approach)
                var componentName = ExtractComponentNameFromCode(componentCode);
                var propsInterface = $"{componentName}Props";

                // Generate props interface
                typeBuilder.AppendLine($"interface {propsInterface} {{");
                typeBuilder.AppendLine("  data: any;");
                typeBuilder.AppendLine("  className?: string;");

                // Add basic props
                typeBuilder.AppendLine("  style?: React.CSSProperties;");
                typeBuilder.AppendLine("  [key: string]: any;");
                typeBuilder.AppendLine("}");

                // Add component type
                typeBuilder.AppendLine();
                typeBuilder.AppendLine($"export type {componentName}Component = React.FC<{propsInterface}>;");

                var result = typeBuilder.ToString();
                _logger.LogDebug("Generated type definitions for component {ComponentName}", componentName);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating type definitions for component");
                return string.Empty;
            }
        }

        /// <summary>
        /// Validates a generated React component for syntax and best practices
        /// </summary>
        public async Task<FY.WB.CSHero2.Domain.Interfaces.ValidationResult> ValidateComponentAsync(string componentCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Validating component code");

                var result = new FY.WB.CSHero2.Domain.Interfaces.ValidationResult
                {
                    IsValid = true,
                    Errors = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationError>(),
                    Warnings = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationWarning>(),
                    Suggestions = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationSuggestion>(),
                    QualityScore = new FY.WB.CSHero2.Domain.Interfaces.ComponentQualityScore
                    {
                        OverallScore = 85,
                        PerformanceScore = 80,
                        AccessibilityScore = 75,
                        MaintainabilityScore = 90,
                        BestPracticesScore = 85
                    }
                };

                // Basic validation checks
                if (string.IsNullOrEmpty(componentCode))
                {
                    result.IsValid = false;
                    result.Errors.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationError
                    {
                        Code = "EMPTY_COMPONENT",
                        Message = "Component code is empty",
                        Severity = "Error"
                    });
                    return result;
                }

                // Check for required React component structure
                if (!componentCode.Contains("export"))
                {
                    result.Errors.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationError
                    {
                        Code = "NO_EXPORT",
                        Message = "Component must have an export statement",
                        Severity = "Error"
                    });
                    result.IsValid = false;
                }

                if (!componentCode.Contains("return"))
                {
                    result.Errors.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationError
                    {
                        Code = "NO_RETURN",
                        Message = "Component must have a return statement",
                        Severity = "Error"
                    });
                    result.IsValid = false;
                }

                // Check for best practices
                if (!componentCode.Contains("React.FC") && !componentCode.Contains("function"))
                {
                    result.Warnings.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationWarning
                    {
                        Code = "COMPONENT_TYPE",
                        Message = "Consider using React.FC or function declaration for better type safety",
                        Suggestion = "Use React.FC<Props> or function ComponentName(props: Props)"
                    });
                }

                // Check for accessibility
                if (!componentCode.Contains("aria-") && !componentCode.Contains("role="))
                {
                    result.Suggestions.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationSuggestion
                    {
                        Type = "Accessibility",
                        Message = "Consider adding ARIA attributes for better accessibility",
                        Priority = 2
                    });
                    result.QualityScore.AccessibilityScore -= 10;
                }

                // Check for performance optimizations
                if (componentCode.Contains("useState") && !componentCode.Contains("useCallback"))
                {
                    result.Suggestions.Add(new FY.WB.CSHero2.Domain.Interfaces.ValidationSuggestion
                    {
                        Type = "Performance",
                        Message = "Consider using useCallback for event handlers to prevent unnecessary re-renders",
                        Priority = 1
                    });
                }

                _logger.LogDebug("Component validation completed with {ErrorCount} errors and {WarningCount} warnings",
                    result.Errors.Count, result.Warnings.Count);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating component");
                return new FY.WB.CSHero2.Domain.Interfaces.ValidationResult
                {
                    IsValid = false,
                    Errors = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationError>
                    {
                        new FY.WB.CSHero2.Domain.Interfaces.ValidationError
                        {
                            Code = "VALIDATION_ERROR",
                            Message = $"Validation failed: {ex.Message}",
                            Severity = "Error"
                        }
                    },
                    Warnings = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationWarning>(),
                    Suggestions = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationSuggestion>()
                };
            }
        }

        /// <summary>
        /// Optimizes a component for performance
        /// </summary>
        public async Task<string> OptimizeComponentAsync(string componentCode, OptimizationOptions options, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Optimizing component code");

                var optimizedCode = componentCode;

                if (options.OptimizeImports)
                {
                    optimizedCode = OptimizeImports(optimizedCode);
                }

                if (options.RemoveUnusedCode)
                {
                    optimizedCode = RemoveUnusedCode(optimizedCode);
                }

                if (options.OptimizePerformance)
                {
                    optimizedCode = AddPerformanceOptimizations(optimizedCode);
                }

                if (options.MinifyCode)
                {
                    optimizedCode = MinifyCode(optimizedCode);
                }

                _logger.LogDebug("Component optimization completed");

                return await Task.FromResult(optimizedCode);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing component");
                return componentCode; // Return original code if optimization fails
            }
        }

        /// <summary>
        /// Generates component tests
        /// </summary>
        public async Task<string> GenerateComponentTestsAsync(string componentCode, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating tests for component code");

                var componentName = ExtractComponentNameFromCode(componentCode);
                var testBuilder = new StringBuilder();

                // Generate test file header
                testBuilder.AppendLine($"import {{ render, screen }} from '@testing-library/react';");
                testBuilder.AppendLine($"import {{ {componentName} }} from './{componentName}';");
                testBuilder.AppendLine();

                // Generate basic test
                testBuilder.AppendLine($"describe('{componentName}', () => {{");
                testBuilder.AppendLine($"  it('renders without crashing', () => {{");
                testBuilder.AppendLine($"    const mockData = {{}};");
                testBuilder.AppendLine($"    render(<{componentName} data={{mockData}} />);");
                testBuilder.AppendLine($"  }});");
                testBuilder.AppendLine();

                // Generate data test
                testBuilder.AppendLine($"  it('displays data correctly', () => {{");
                testBuilder.AppendLine($"    const mockData = {{ title: 'Test Title' }};");
                testBuilder.AppendLine($"    render(<{componentName} data={{mockData}} />);");
                testBuilder.AppendLine($"    expect(screen.getByText('Test Title')).toBeInTheDocument();");
                testBuilder.AppendLine($"  }});");
                testBuilder.AppendLine();

                // Generate accessibility test
                testBuilder.AppendLine($"  it('is accessible', () => {{");
                testBuilder.AppendLine($"    const mockData = {{}};");
                testBuilder.AppendLine($"    const {{ container }} = render(<{componentName} data={{mockData}} />);");
                testBuilder.AppendLine($"    // Add accessibility assertions here");
                testBuilder.AppendLine($"  }});");
                testBuilder.AppendLine("});");

                var result = testBuilder.ToString();
                _logger.LogDebug("Generated tests for component {ComponentName}", componentName);

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating tests for component");
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets available component templates
        /// </summary>
        public async Task<List<ComponentTemplate>> GetComponentTemplatesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting component templates");

                var templates = new List<ComponentTemplate>
                {
                    new ComponentTemplate
                    {
                        Id = "chart-template",
                        Name = "Chart Component",
                        Description = "Template for chart components",
                        Category = "Data Visualization",
                        RequiredProps = new List<string> { "data", "chartType" },
                        Tags = new List<string> { "chart", "visualization", "data" }
                    },
                    new ComponentTemplate
                    {
                        Id = "table-template",
                        Name = "Table Component",
                        Description = "Template for table components",
                        Category = "Data Display",
                        RequiredProps = new List<string> { "data", "columns" },
                        Tags = new List<string> { "table", "data", "grid" }
                    },
                    new ComponentTemplate
                    {
                        Id = "text-template",
                        Name = "Text Component",
                        Description = "Template for text sections",
                        Category = "Content",
                        RequiredProps = new List<string> { "content" },
                        Tags = new List<string> { "text", "content", "typography" }
                    }
                };

                return await Task.FromResult(templates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting component templates");
                return new List<ComponentTemplate>();
            }
        }

        /// <summary>
        /// Previews a component without saving it
        /// </summary>
        public async Task<ComponentPreview> PreviewComponentAsync(ComponentPreviewRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Generating component preview");

                // Validate the component
                var validationResult = await ValidateComponentAsync(request.ComponentCode, cancellationToken);

                var preview = new ComponentPreview
                {
                    ValidationResult = validationResult,
                    RequiredDependencies = ExtractDependencies(request.ComponentCode),
                    Metadata = new ComponentMetadata
                    {
                        Framework = request.Options.Framework,
                        StyleFramework = request.Options.StyleFramework,
                        GeneratedAt = DateTime.UtcNow
                    }
                };

                // Generate a simple HTML preview (in a real implementation, this would render the React component)
                preview.RenderedHtml = GenerateHtmlPreview(request.ComponentCode, request.SampleData);
                preview.PreviewUrl = $"/api/preview/{Guid.NewGuid()}"; // Mock preview URL

                _logger.LogDebug("Component preview generated successfully");

                return await Task.FromResult(preview);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating component preview");
                return new ComponentPreview
                {
                    ValidationResult = new FY.WB.CSHero2.Domain.Interfaces.ValidationResult
                    {
                        IsValid = false,
                        Errors = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationError>
                        {
                            new FY.WB.CSHero2.Domain.Interfaces.ValidationError
                            {
                                Code = "PREVIEW_ERROR",
                                Message = $"Preview generation failed: {ex.Message}",
                                Severity = "Error"
                            }
                        },
                        Warnings = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationWarning>(),
                        Suggestions = new List<FY.WB.CSHero2.Domain.Interfaces.ValidationSuggestion>()
                    }
                };
            }
        }

        #region Private Helper Methods

        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;

            var words = input.Split(new[] { ' ', '-', '_' }, StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();

            foreach (var word in words)
            {
                if (word.Length > 0)
                {
                    result.Append(char.ToUpper(word[0]));
                    if (word.Length > 1)
                    {
                        result.Append(word.Substring(1).ToLower());
                    }
                }
            }

            return result.ToString();
        }

        private string InferTypeScriptType(object value)
        {
            return value switch
            {
                string => "string",
                int or long or float or double or decimal => "number",
                bool => "boolean",
                Array => "any[]",
                _ => "any"
            };
        }

        private string OptimizeImports(string code)
        {
            // Simple import optimization - remove duplicates and sort
            var lines = code.Split('\n');
            var imports = lines.Where(l => l.Trim().StartsWith("import")).Distinct().OrderBy(l => l);
            var nonImports = lines.Where(l => !l.Trim().StartsWith("import"));

            return string.Join('\n', imports.Concat(new[] { "" }).Concat(nonImports));
        }

        private string RemoveUnusedCode(string code)
        {
            // Basic unused code removal - remove empty lines and comments
            var lines = code.Split('\n')
                .Where(l => !string.IsNullOrWhiteSpace(l) && !l.Trim().StartsWith("//"))
                .ToArray();

            return string.Join('\n', lines);
        }

        private string AddPerformanceOptimizations(string code)
        {
            // Add React.memo if not present
            if (!code.Contains("React.memo") && code.Contains("export const"))
            {
                code = code.Replace("export const", "export const");
                // In a real implementation, this would add React.memo wrapper
            }

            return code;
        }

        private string MinifyCode(string code)
        {
            // Basic minification - remove extra whitespace
            return System.Text.RegularExpressions.Regex.Replace(code, @"\s+", " ").Trim();
        }

        private List<string> ExtractDependencies(string componentCode)
        {
            var dependencies = new List<string>();

            if (componentCode.Contains("React")) dependencies.Add("react");
            if (componentCode.Contains("useState") || componentCode.Contains("useEffect")) dependencies.Add("react");
            if (componentCode.Contains("className")) dependencies.Add("classnames");

            return dependencies.Distinct().ToList();
        }

        private string GenerateHtmlPreview(string componentCode, Dictionary<string, object> sampleData)
        {
            // Generate a simple HTML representation of the component
            return $@"
                <div class='component-preview'>
                    <h3>Component Preview</h3>
                    <div class='component-content'>
                        <p>Component would render here with data:</p>
                        <pre>{System.Text.Json.JsonSerializer.Serialize(sampleData, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}</pre>
                    </div>
                </div>";
        }

        private string ExtractComponentNameFromCode(string componentCode)
        {
            if (string.IsNullOrEmpty(componentCode))
                return "UnknownComponent";

            // Simple regex to extract component name from React component code
            var match = System.Text.RegularExpressions.Regex.Match(
                componentCode,
                @"(?:export\s+(?:default\s+)?(?:const|function)\s+|function\s+)(\w+)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            return match.Success ? match.Groups[1].Value : "UnknownComponent";
        }

        #endregion
    }
}
