using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Enhanced versioning service with advanced comparison and metadata capabilities
    /// </summary>
    public class EnhancedVersioningService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EnhancedVersioningService> _logger;
        private readonly IReportMetadataRepository _metadataRepository;
        private readonly IReportComponentsRepository _componentsRepository;

        public EnhancedVersioningService(
            ApplicationDbContext context,
            ILogger<EnhancedVersioningService> logger,
            IReportMetadataRepository metadataRepository,
            IReportComponentsRepository componentsRepository)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _metadataRepository = metadataRepository ?? throw new ArgumentNullException(nameof(metadataRepository));
            _componentsRepository = componentsRepository ?? throw new ArgumentNullException(nameof(componentsRepository));
        }

        /// <summary>
        /// Compares two versions of a report and returns detailed differences
        /// </summary>
        public async Task<FY.WB.CSHero2.ReportRenderingEngine.Domain.Models.VersionComparison> CompareVersionsAsync(
            Guid reportId,
            int version1,
            int version2,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Comparing versions {Version1} and {Version2} for report {ReportId}",
                    version1, version2, reportId);

                var v1 = await _metadataRepository.GetVersionAsync(reportId, version1, cancellationToken);
                var v2 = await _metadataRepository.GetVersionAsync(reportId, version2, cancellationToken);

                if (v1 == null || v2 == null)
                {
                    throw new InvalidOperationException($"One or both versions not found for report {reportId}");
                }

                var comparison = new FY.WB.CSHero2.ReportRenderingEngine.Domain.Models.VersionComparison
                {
                    ReportId = reportId,
                    Version1 = version1,
                    Version2 = version2,
                    ComparedAt = DateTime.UtcNow
                };

                // Compare basic metadata
                comparison.MetadataChanges = CompareMetadata(v1, v2);

                // Compare component data
                comparison.ComponentChanges = await CompareComponentDataAsync(v1, v2, cancellationToken);

                // Compare report data
                comparison.DataChanges = CompareReportData(v1, v2);

                // Calculate overall change summary
                comparison.Summary = GenerateChangeSummary(comparison);

                _logger.LogInformation("Version comparison completed for report {ReportId}: {ChangeCount} changes found",
                    reportId, comparison.Summary.TotalChanges);

                return comparison;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error comparing versions {Version1} and {Version2} for report {ReportId}",
                    version1, version2, reportId);
                throw;
            }
        }

        /// <summary>
        /// Creates a version with enhanced metadata and change tracking
        /// </summary>
        public async Task<ReportVersion> CreateVersionWithMetadataAsync(
            Guid reportId,
            ComponentResult components,
            VersionMetadata metadata,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Creating enhanced version for report {ReportId} with metadata", reportId);

                // Get the report
                var report = await _context.Reports
                    .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

                if (report == null)
                {
                    throw new InvalidOperationException($"Report with ID {reportId} not found");
                }

                // Get the next version number
                var latestVersionNumber = await _context.ReportVersions
                    .Where(rv => rv.ReportId == reportId)
                    .MaxAsync(rv => (int?)rv.VersionNumber, cancellationToken) ?? 0;

                var newVersionNumber = latestVersionNumber + 1;

                // Mark all existing versions as not current
                var existingVersions = await _context.ReportVersions
                    .Where(rv => rv.ReportId == reportId && rv.IsCurrent)
                    .ToListAsync(cancellationToken);

                foreach (var version in existingVersions)
                {
                    version.SetAsNotCurrent();
                }

                // Create new version with enhanced metadata
                var newVersion = new ReportVersion
                {
                    ReportId = reportId,
                    VersionNumber = newVersionNumber,
                    Description = metadata.Description ?? $"Version {newVersionNumber}",
                    IsCurrent = true
                };

                // Set component data
                newVersion.SetComponentData(components);

                // Set report data
                if (components.Data.Any())
                {
                    newVersion.SetReportData(components.Data);
                }

                // Add enhanced metadata
                await AddVersionMetadataAsync(newVersion, metadata, cancellationToken);

                // Calculate change summary if previous version exists
                if (latestVersionNumber > 0)
                {
                    var previousVersion = await _context.ReportVersions
                        .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.VersionNumber == latestVersionNumber,
                            cancellationToken);

                    if (previousVersion != null)
                    {
                        var changeSummary = await CalculateChangeSummaryAsync(previousVersion, newVersion, cancellationToken);
                        metadata.ChangeSummary = changeSummary;
                    }
                }

                _context.ReportVersions.Add(newVersion);

                // Update report's current version
                report.CurrentVersionId = newVersion.Id;

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created enhanced version {VersionNumber} for report {ReportId}",
                    newVersionNumber, reportId);

                return newVersion;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating enhanced version for report {ReportId}", reportId);
                throw;
            }
        }

        /// <summary>
        /// Gets version history with enhanced metadata
        /// </summary>
        public async Task<List<VersionHistoryEntry>> GetEnhancedVersionHistoryAsync(
            Guid reportId,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting enhanced version history for report {ReportId}", reportId);

                var versions = await _metadataRepository.GetVersionHistoryAsync(reportId, cancellationToken);
                versions = versions.OrderByDescending(rv => rv.VersionNumber).ToList();

                var historyEntries = new List<VersionHistoryEntry>();

                for (int i = 0; i < versions.Count; i++)
                {
                    var version = versions[i];

                    // Get component count from blob storage if available
                    int componentCount = 0;
                    if (!string.IsNullOrEmpty(version.ComponentsBlobId))
                    {
                        try
                        {
                            var (_, _, count) = await _componentsRepository.GetComponentsInfoAsync(version.ComponentsBlobId, cancellationToken);
                            componentCount = count;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Could not get component count for version {VersionId}", version.Id);
                        }
                    }

                    var entry = new VersionHistoryEntry
                    {
                        VersionNumber = version.VersionNumber,
                        Description = version.Description,
                        CreatedAt = version.CreationTime,
                        CreatedBy = version.CreatorId ?? Guid.Empty,
                        IsCurrent = version.IsCurrent,
                        ComponentCount = componentCount,
                        DataSize = version.ComponentDataSize + version.JsonDataSize
                    };

                    // Calculate changes from previous version
                    if (i < versions.Count - 1)
                    {
                        var previousVersion = versions[i + 1];
                        entry.ChangesFromPrevious = await CalculateChangeSummaryAsync(previousVersion, version, cancellationToken);
                    }

                    historyEntries.Add(entry);
                }

                _logger.LogDebug("Retrieved {Count} version history entries for report {ReportId}",
                    historyEntries.Count, reportId);

                return historyEntries;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting enhanced version history for report {ReportId}", reportId);
                throw;
            }
        }

        /// <summary>
        /// Creates a branch from an existing version
        /// </summary>
        public async Task<ReportVersion> CreateBranchAsync(
            Guid reportId,
            int sourceVersionNumber,
            string branchName,
            string description,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Creating branch '{BranchName}' from version {SourceVersion} for report {ReportId}",
                    branchName, sourceVersionNumber, reportId);

                var sourceVersion = await _metadataRepository.GetVersionAsync(reportId, sourceVersionNumber, cancellationToken);

                if (sourceVersion == null)
                {
                    throw new InvalidOperationException($"Source version {sourceVersionNumber} not found for report {reportId}");
                }

                // Get the next version number
                var latestVersionNumber = await _context.ReportVersions
                    .Where(rv => rv.ReportId == reportId)
                    .MaxAsync(rv => (int?)rv.VersionNumber, cancellationToken) ?? 0;

                var newVersionNumber = latestVersionNumber + 1;

                // Create branch version
                var branchVersion = sourceVersion.CreateCopy(newVersionNumber, description, sourceVersion.CreatorId ?? Guid.Empty);
                branchVersion.Description = $"Branch '{branchName}': {description}";

                // Add branch metadata
                var metadata = new VersionMetadata
                {
                    Description = branchVersion.Description,
                    BranchName = branchName,
                    SourceVersionNumber = sourceVersionNumber,
                    IsBranch = true,
                    Tags = new List<string> { "branch", branchName }
                };

                await AddVersionMetadataAsync(branchVersion, metadata, cancellationToken);

                _context.ReportVersions.Add(branchVersion);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created branch '{BranchName}' as version {VersionNumber} for report {ReportId}",
                    branchName, newVersionNumber, reportId);

                return branchVersion;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating branch '{BranchName}' for report {ReportId}", branchName, reportId);
                throw;
            }
        }

        #region Private Helper Methods

        private List<MetadataChange> CompareMetadata(ReportVersion v1, ReportVersion v2)
        {
            var changes = new List<MetadataChange>();

            if (v1.Description != v2.Description)
            {
                changes.Add(new MetadataChange
                {
                    Property = "Description",
                    OldValue = v1.Description,
                    NewValue = v2.Description,
                    ChangeType = "Modified"
                });
            }

            if (v1.ComponentDataSize != v2.ComponentDataSize)
            {
                changes.Add(new MetadataChange
                {
                    Property = "ComponentDataSize",
                    OldValue = v1.ComponentDataSize.ToString(),
                    NewValue = v2.ComponentDataSize.ToString(),
                    ChangeType = "Modified"
                });
            }

            if (v1.JsonDataSize != v2.JsonDataSize)
            {
                changes.Add(new MetadataChange
                {
                    Property = "JsonDataSize",
                    OldValue = v1.JsonDataSize.ToString(),
                    NewValue = v2.JsonDataSize.ToString(),
                    ChangeType = "Modified"
                });
            }

            return changes;
        }

        private async Task<List<ComponentChange>> CompareComponentDataAsync(
            ReportVersion v1,
            ReportVersion v2,
            CancellationToken cancellationToken)
        {
            var changes = new List<ComponentChange>();

            try
            {
                var components1 = v1.GetComponentData<ComponentResult>() ?? new ComponentResult();
                var components2 = v2.GetComponentData<ComponentResult>() ?? new ComponentResult();

                // Compare component counts
                if (components1.Components.Count != components2.Components.Count)
                {
                    changes.Add(new ComponentChange
                    {
                        SectionId = "GLOBAL",
                        ChangeType = "ComponentCountChanged",
                        Description = $"Component count changed from {components1.Components.Count} to {components2.Components.Count}"
                    });
                }

                // Compare individual components
                var allSectionIds = components1.Components.Select(c => c.SectionId)
                    .Union(components2.Components.Select(c => c.SectionId))
                    .Distinct();

                foreach (var sectionId in allSectionIds)
                {
                    var comp1 = components1.Components.FirstOrDefault(c => c.SectionId == sectionId);
                    var comp2 = components2.Components.FirstOrDefault(c => c.SectionId == sectionId);

                    if (comp1 == null && comp2 != null)
                    {
                        changes.Add(new ComponentChange
                        {
                            SectionId = sectionId,
                            ChangeType = "Added",
                            Description = $"Component added for section {sectionId}"
                        });
                    }
                    else if (comp1 != null && comp2 == null)
                    {
                        changes.Add(new ComponentChange
                        {
                            SectionId = sectionId,
                            ChangeType = "Removed",
                            Description = $"Component removed for section {sectionId}"
                        });
                    }
                    else if (comp1 != null && comp2 != null)
                    {
                        if (comp1.ComponentCode != comp2.ComponentCode)
                        {
                            changes.Add(new ComponentChange
                            {
                                SectionId = sectionId,
                                ChangeType = "Modified",
                                Description = $"Component code changed for section {sectionId}"
                            });
                        }

                        if (comp1.TypeDefinitions != comp2.TypeDefinitions)
                        {
                            changes.Add(new ComponentChange
                            {
                                SectionId = sectionId,
                                ChangeType = "TypesModified",
                                Description = $"Type definitions changed for section {sectionId}"
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error comparing component data between versions");
                changes.Add(new ComponentChange
                {
                    SectionId = "ERROR",
                    ChangeType = "ComparisonError",
                    Description = "Error occurred while comparing component data"
                });
            }

            return changes;
        }

        private List<DataChange> CompareReportData(ReportVersion v1, ReportVersion v2)
        {
            var changes = new List<DataChange>();

            try
            {
                var data1 = v1.GetReportData();
                var data2 = v2.GetReportData();

                var allKeys = data1.Keys.Union(data2.Keys).Distinct();

                foreach (var key in allKeys)
                {
                    var hasValue1 = data1.TryGetValue(key, out var value1);
                    var hasValue2 = data2.TryGetValue(key, out var value2);

                    if (!hasValue1 && hasValue2)
                    {
                        changes.Add(new DataChange
                        {
                            FieldName = key,
                            ChangeType = "Added",
                            NewValue = value2?.ToString() ?? "null"
                        });
                    }
                    else if (hasValue1 && !hasValue2)
                    {
                        changes.Add(new DataChange
                        {
                            FieldName = key,
                            ChangeType = "Removed",
                            OldValue = value1?.ToString() ?? "null"
                        });
                    }
                    else if (hasValue1 && hasValue2)
                    {
                        var str1 = JsonSerializer.Serialize(value1);
                        var str2 = JsonSerializer.Serialize(value2);

                        if (str1 != str2)
                        {
                            changes.Add(new DataChange
                            {
                                FieldName = key,
                                ChangeType = "Modified",
                                OldValue = str1,
                                NewValue = str2
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error comparing report data between versions");
                changes.Add(new DataChange
                {
                    FieldName = "ERROR",
                    ChangeType = "ComparisonError",
                    OldValue = "Error occurred while comparing data",
                    NewValue = ex.Message
                });
            }

            return changes;
        }

        private ChangeSummary GenerateChangeSummary(FY.WB.CSHero2.ReportRenderingEngine.Domain.Models.VersionComparison comparison)
        {
            return new ChangeSummary
            {
                TotalChanges = comparison.MetadataChanges.Count +
                              comparison.ComponentChanges.Count +
                              comparison.DataChanges.Count,
                MetadataChanges = comparison.MetadataChanges.Count,
                ComponentChanges = comparison.ComponentChanges.Count,
                DataChanges = comparison.DataChanges.Count,
                HasBreakingChanges = comparison.ComponentChanges.Any(c => c.ChangeType == "Removed"),
                ChangeTypes = comparison.ComponentChanges.Select(c => c.ChangeType)
                    .Union(comparison.DataChanges.Select(d => d.ChangeType))
                    .Distinct().ToList()
            };
        }

        private async Task AddVersionMetadataAsync(
            ReportVersion version,
            VersionMetadata metadata,
            CancellationToken cancellationToken)
        {
            // Store metadata as JSON in a separate field or table
            // For now, we'll extend the description with metadata
            var metadataJson = JsonSerializer.Serialize(metadata);

            // In a full implementation, you might want to create a separate VersionMetadata table
            // For now, we'll store it in the description or create component definitions for metadata

            await Task.CompletedTask; // Placeholder for metadata storage implementation
        }

        private async Task<ChangeSummary> CalculateChangeSummaryAsync(
            ReportVersion previousVersion,
            ReportVersion currentVersion,
            CancellationToken cancellationToken)
        {
            var comparison = await CompareVersionsAsync(
                previousVersion.ReportId,
                previousVersion.VersionNumber,
                currentVersion.VersionNumber,
                cancellationToken);

            return comparison.Summary;
        }

        #endregion
    }
}
