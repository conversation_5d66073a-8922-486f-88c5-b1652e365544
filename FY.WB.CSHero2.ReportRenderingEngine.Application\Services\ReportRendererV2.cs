using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Enhanced report renderer for V2 with React component generation and partial rendering capabilities
    /// </summary>
    public class ReportRendererV2 : IReportRenderer
    {
        private readonly ILogger<ReportRendererV2> _logger;
        private readonly IReportService _reportService;
        private readonly ITemplateService _templateService;
        private readonly IVersioningService _versioningService;
        private readonly IComponentGenerator _componentGenerator;
        private readonly IMemoryCache _cache;
        private readonly IReportComponentsRepository _componentsRepository;

        public ReportRendererV2(
            ILogger<ReportRendererV2> logger,
            IReportService reportService,
            ITemplateService templateService,
            IVersioningService versioningService,
            IComponentGenerator componentGenerator,
            IMemoryCache cache,
            IReportComponentsRepository componentsRepository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _reportService = reportService ?? throw new ArgumentNullException(nameof(reportService));
            _templateService = templateService ?? throw new ArgumentNullException(nameof(templateService));
            _versioningService = versioningService ?? throw new ArgumentNullException(nameof(versioningService));
            _componentGenerator = componentGenerator ?? throw new ArgumentNullException(nameof(componentGenerator));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _componentsRepository = componentsRepository ?? throw new ArgumentNullException(nameof(componentsRepository));
        }

        /// <summary>
        /// Renders a complete report with all sections
        /// </summary>
        public async Task<ComponentResult> RenderAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogInformation("Starting full report rendering for ReportId: {ReportId}", reportId);

                // Get report and current data
                var report = await _reportService.GetReportAsync(reportId, cancellationToken);
                var currentData = await _reportService.GetReportDataAsync(reportId, cancellationToken);

                // Get template if report is template-based
                Template? template = null;
                if (report.TemplateId.HasValue)
                {
                    template = await _templateService.GetTemplateAsync(report.TemplateId.Value, cancellationToken);
                }

                // Build component request
                var request = new ComponentRequest
                {
                    ReportId = reportId,
                    Data = currentData,
                    Template = template,
                    Options = GetDefaultGenerationOptions()
                };

                // Generate components
                var result = await _componentGenerator.GenerateComponentAsync(request, cancellationToken);

                if (result.Success)
                {
                    // Create new version with generated components
                    var version = await _versioningService.CreateVersionAsync(
                        reportId,
                        result,
                        "Full report re-render",
                        cancellationToken);

                    _logger.LogInformation("Created version {VersionNumber} for report {ReportId}",
                        version.VersionNumber, reportId);
                }

                stopwatch.Stop();
                // Note: Metrics tracking would be added to ComponentResult in future enhancement

                _logger.LogInformation("Full rendering completed for ReportId: {ReportId} in {ElapsedMs}ms",
                    reportId, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during full rendering for ReportId: {ReportId}", reportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Rendering failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Renders a specific section of a report
        /// </summary>
        public async Task<ComponentResult> RenderSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogInformation("Starting section rendering for ReportId: {ReportId}, Section: {SectionId}",
                    reportId, sectionId);

                // Get report and current data
                var report = await _reportService.GetReportAsync(reportId, cancellationToken);
                var currentData = await _reportService.GetReportDataAsync(reportId, cancellationToken);
                var currentVersion = await _versioningService.GetCurrentVersionAsync(reportId, cancellationToken);

                // Get existing component for this section from blob storage
                FY.WB.CSHero2.ReportRenderingEngine.Domain.Models.ComponentDefinition? existingComponent = null;
                if (!string.IsNullOrEmpty(currentVersion.ComponentsBlobId))
                {
                    try
                    {
                        existingComponent = await _componentsRepository.GetComponentAsync(
                            reportId, currentVersion.Id, report.TenantId ?? Guid.Empty, sectionId, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Could not retrieve existing component for section {SectionId}", sectionId);
                    }
                }

                // Build section request
                var request = new SectionComponentRequest
                {
                    ReportId = reportId,
                    SectionId = sectionId,
                    SectionName = existingComponent?.Name ?? sectionId,
                    Data = currentData,
                    Options = GetDefaultGenerationOptions(),
                    ExistingComponentCode = existingComponent?.ComponentCode,
                    PreserveExistingStructure = existingComponent != null
                };

                // Generate section component
                var result = await _componentGenerator.GenerateSectionComponentAsync(request, cancellationToken);

                if (result.Success)
                {
                    // Create new version with updated section
                    var version = await _versioningService.CreateVersionAsync(
                        reportId,
                        result,
                        $"Section re-render: {sectionId}",
                        cancellationToken);

                    _logger.LogInformation("Created version {VersionNumber} for section {SectionId} in report {ReportId}",
                        version.VersionNumber, sectionId, reportId);
                }

                stopwatch.Stop();
                // Note: Metrics tracking would be added to ComponentResult in future enhancement

                _logger.LogInformation("Section rendering completed for ReportId: {ReportId}, Section: {SectionId} in {ElapsedMs}ms",
                    reportId, sectionId, stopwatch.ElapsedMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during section rendering for ReportId: {ReportId}, Section: {SectionId}",
                    reportId, sectionId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Section rendering failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Renders multiple sections in parallel
        /// </summary>
        public async Task<ComponentResult> RenderSectionsAsync(
            Guid reportId,
            List<string> sectionIds,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                _logger.LogInformation("Starting multi-section rendering for ReportId: {ReportId}, Sections: {SectionIds}",
                    reportId, string.Join(", ", sectionIds));

                // Render sections in parallel
                var renderTasks = sectionIds.Select(sectionId =>
                    RenderSectionAsync(reportId, sectionId, cancellationToken)).ToArray();

                var sectionResults = await Task.WhenAll(renderTasks);

                // Combine results
                var combinedResult = new ComponentResult
                {
                    Success = sectionResults.All(r => r.Success),
                    Components = sectionResults.SelectMany(r => r.Components).ToList(),
                    Errors = sectionResults.SelectMany(r => r.Errors).ToList(),
                    Warnings = sectionResults.SelectMany(r => r.Warnings).ToList()
                };

                if (combinedResult.Success)
                {
                    // Create new version with all updated sections
                    var version = await _versioningService.CreateVersionAsync(
                        reportId,
                        combinedResult,
                        $"Multi-section re-render: {string.Join(", ", sectionIds)}",
                        cancellationToken);

                    _logger.LogInformation("Created version {VersionNumber} for multi-section render in report {ReportId}",
                        version.VersionNumber, reportId);
                }

                _logger.LogInformation("Multi-section rendering completed for ReportId: {ReportId} in {ElapsedMs}ms",
                    reportId, stopwatch.ElapsedMilliseconds);

                return combinedResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during multi-section rendering for ReportId: {ReportId}", reportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Multi-section rendering failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Previews component generation without saving
        /// </summary>
        public async Task<ComponentResult> PreviewAsync(
            Guid reportId,
            ComponentGenerationOptions? options = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Starting preview rendering for ReportId: {ReportId}", reportId);

                // Get report and current data
                var report = await _reportService.GetReportAsync(reportId, cancellationToken);
                var currentData = await _reportService.GetReportDataAsync(reportId, cancellationToken);

                // Get template if report is template-based
                Template? template = null;
                if (report.TemplateId.HasValue)
                {
                    template = await _templateService.GetTemplateAsync(report.TemplateId.Value, cancellationToken);
                }

                // Build component request
                var request = new ComponentRequest
                {
                    ReportId = reportId,
                    Data = currentData,
                    Template = template,
                    Options = options ?? GetDefaultGenerationOptions()
                };

                // Generate components (preview only - don't save)
                var result = await _componentGenerator.GenerateComponentAsync(request, cancellationToken);

                _logger.LogInformation("Preview rendering completed for ReportId: {ReportId}", reportId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during preview rendering for ReportId: {ReportId}", reportId);
                return new ComponentResult
                {
                    Success = false,
                    Errors = new List<string> { $"Preview rendering failed: {ex.Message}" }
                };
            }
        }

        /// <summary>
        /// Gets default generation options
        /// </summary>
        private ComponentGenerationOptions GetDefaultGenerationOptions()
        {
            return new ComponentGenerationOptions
            {
                Framework = "NextJS",
                UseTypeScript = true,
                StyleFramework = "TailwindCSS",
                OptimizeForPerformance = true,
                IncludeAccessibility = true,
                IncludeResponsiveDesign = true
            };
        }
    }

    /// <summary>
    /// Interface for the enhanced report renderer
    /// </summary>
    public interface IReportRenderer
    {
        Task<ComponentResult> RenderAsync(Guid reportId, CancellationToken cancellationToken = default);
        Task<ComponentResult> RenderSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    }
}
