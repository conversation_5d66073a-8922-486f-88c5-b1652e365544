using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using FY.WB.CSHero2.Domain.Interfaces;
using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using ReportSection = FY.WB.CSHero2.ReportRenderingEngine.Domain.Models.ReportSection;
using VersionComparison = FY.WB.CSHero2.Domain.Interfaces.VersionComparison;
using SectionComponent = FY.WB.CSHero2.Domain.Interfaces.SectionComponent;

namespace FY.WB.CSHero2.ReportRenderingEngine.Application.Services
{
    /// <summary>
    /// Implementation of versioning service for managing report versions and version control
    /// </summary>
    public class VersioningServiceImpl : IVersioningService
    {
        private readonly ILogger<VersioningServiceImpl> _logger;
        private readonly ApplicationDbContext _context;
        private readonly IReportMetadataRepository _metadataRepository;
        private readonly IReportDataRepository _dataRepository;
        private readonly IReportComponentsRepository _componentsRepository;

        public VersioningServiceImpl(
            ILogger<VersioningServiceImpl> logger,
            ApplicationDbContext context,
            IReportMetadataRepository metadataRepository,
            IReportDataRepository dataRepository,
            IReportComponentsRepository componentsRepository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _metadataRepository = metadataRepository ?? throw new ArgumentNullException(nameof(metadataRepository));
            _dataRepository = dataRepository ?? throw new ArgumentNullException(nameof(dataRepository));
            _componentsRepository = componentsRepository ?? throw new ArgumentNullException(nameof(componentsRepository));
        }

        /// <summary>
        /// Creates a new version of a report with component data
        /// </summary>
        public async Task<ReportVersion> CreateVersionAsync(
            Guid reportId,
            ComponentResult components,
            string? description = null,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Creating new version for report {ReportId}", reportId);

            // Get the report
            var report = await _context.Reports
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report == null)
            {
                throw new InvalidOperationException($"Report with ID {reportId} not found");
            }

            // Get the next version number
            var latestVersionNumber = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .MaxAsync(rv => (int?)rv.VersionNumber, cancellationToken) ?? 0;

            var newVersionNumber = latestVersionNumber + 1;

            // Mark all existing versions as not current
            var existingVersions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId && rv.IsCurrent)
                .ToListAsync(cancellationToken);

            foreach (var version in existingVersions)
            {
                version.SetAsNotCurrent();
            }

            // Create new version
            var newVersion = new ReportVersion
            {
                ReportId = reportId,
                VersionNumber = newVersionNumber,
                Description = description ?? $"Version {newVersionNumber}",
                IsCurrent = true
            };

            // Set component data
            newVersion.SetComponentData(components);

            // Set report data
            if (components.Data.Any())
            {
                newVersion.SetReportData(components.Data);
            }

            // Create version in SQL database first
            var createdVersion = await _metadataRepository.CreateVersionAsync(newVersion, cancellationToken);

            // Create report data in Cosmos DB if we have data
            string? dataDocumentId = null;
            if (components.Data.Any())
            {
                var reportData = new ReportData
                {
                    Id = ReportData.CreateDocumentId(reportId, createdVersion.Id),
                    ReportId = reportId.ToString(),
                    VersionId = createdVersion.Id.ToString(),
                    TenantId = report.TenantId.ToString(),
                    PartitionKey = report.TenantId.ToString(),
                    Sections = ConvertDataToSections(components.Data)
                };

                dataDocumentId = await _dataRepository.CreateReportDataAsync(reportData, cancellationToken);
            }

            // Create components in Blob Storage
            string? componentsBlobId = null;
            if (components.Components.Any())
            {
                var componentDefinitions = components.Components.Select(c => new ComponentDefinition
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = c.SectionName,
                    SectionId = c.SectionId,
                    ComponentCode = c.ComponentCode,
                    TypeDefinitions = c.TypeDefinitions,
                    Imports = c.Imports,
                    Hash = CalculateComponentHash(c),
                    Framework = "NextJS",
                    StyleFramework = "TailwindCSS",
                    IsValid = true
                }).ToList();

                componentsBlobId = await _componentsRepository.SaveComponentsAsync(
                    reportId, createdVersion.Id, report.TenantId ?? Guid.Empty, componentDefinitions, cancellationToken);
            }

            // Update storage references in SQL
            await _metadataRepository.UpdateStorageReferencesAsync(
                createdVersion.Id, dataDocumentId, componentsBlobId, null, cancellationToken);

            // Update report's current version
            report.CurrentVersionId = createdVersion.Id;
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully created version {VersionNumber} for report {ReportId}",
                newVersionNumber, reportId);

            return newVersion;
        }

        /// <summary>
        /// Gets the version history for a report
        /// </summary>
        public async Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version history for report {ReportId}", reportId);

            return await _metadataRepository.GetVersionHistoryAsync(reportId, cancellationToken);
        }

        /// <summary>
        /// Gets a specific version of a report
        /// </summary>
        public async Task<ReportVersion> GetVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version {VersionNumber} for report {ReportId}", versionNumber, reportId);

            var version = await _metadataRepository.GetVersionAsync(reportId, versionNumber, cancellationToken);

            if (version == null)
            {
                throw new InvalidOperationException($"Version {versionNumber} not found for report {reportId}");
            }

            return version;
        }

        /// <summary>
        /// Rolls back a report to a previous version
        /// </summary>
        public async Task<bool> RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Rolling back report {ReportId} to version {VersionNumber}", reportId, versionNumber);

            var targetVersion = await GetVersionAsync(reportId, versionNumber, cancellationToken);

            // Mark all versions as not current
            var allVersions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .ToListAsync(cancellationToken);

            foreach (var version in allVersions)
            {
                version.SetAsNotCurrent();
            }

            // Mark target version as current
            targetVersion.SetAsCurrent();

            // Update report's current version
            var report = await _context.Reports
                .FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);

            if (report != null)
            {
                report.CurrentVersionId = targetVersion.Id;
                // LastModificationTime will be set by EF Core interceptors
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully rolled back report {ReportId} to version {VersionNumber}",
                reportId, versionNumber);

            return true;
        }

        /// <summary>
        /// Deletes a specific version (cannot delete current version or version 1)
        /// </summary>
        public async Task DeleteVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Deleting version {VersionNumber} for report {ReportId}", versionNumber, reportId);

            var version = await GetVersionAsync(reportId, versionNumber, cancellationToken);

            if (!version.CanBeDeleted())
            {
                throw new InvalidOperationException($"Version {versionNumber} cannot be deleted");
            }

            _context.ReportVersions.Remove(version);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully deleted version {VersionNumber} for report {ReportId}",
                versionNumber, reportId);
        }

        /// <summary>
        /// Gets the current version of a report
        /// </summary>
        public async Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting current version for report {ReportId}", reportId);

            var currentVersion = await _metadataRepository.GetCurrentVersionAsync(reportId, cancellationToken);

            if (currentVersion == null)
            {
                throw new InvalidOperationException($"No current version found for report {reportId}");
            }

            return currentVersion;
        }

        /// <summary>
        /// Compares two versions of a report
        /// </summary>
        public async Task<VersionComparison> CompareVersionsAsync(
            Guid reportId,
            int fromVersion,
            int toVersion,
            CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Comparing versions {FromVersion} to {ToVersion} for report {ReportId}",
                fromVersion, toVersion, reportId);

            var fromVersionEntity = await GetVersionAsync(reportId, fromVersion, cancellationToken);
            var toVersionEntity = await GetVersionAsync(reportId, toVersion, cancellationToken);

            var comparison = new VersionComparison
            {
                FromVersion = fromVersionEntity,
                ToVersion = toVersionEntity
            };

            // Compare data changes
            var fromData = fromVersionEntity.GetReportData();
            var toData = toVersionEntity.GetReportData();

            foreach (var key in fromData.Keys.Union(toData.Keys))
            {
                var fromValue = fromData.GetValueOrDefault(key);
                var toValue = toData.GetValueOrDefault(key);

                if (!Equals(fromValue, toValue))
                {
                    comparison.DataChanges.Add($"Field '{key}' changed from '{fromValue}' to '{toValue}'");
                }
            }

            // Compare component changes using blob storage
            if (!string.IsNullOrEmpty(fromVersionEntity.ComponentsBlobId) && !string.IsNullOrEmpty(toVersionEntity.ComponentsBlobId))
            {
                var componentDifferences = await _componentsRepository.CompareComponentsAsync(
                    fromVersionEntity.ComponentsBlobId, toVersionEntity.ComponentsBlobId, cancellationToken);
                comparison.ComponentChanges.AddRange(componentDifferences);
            }
            else if (!string.IsNullOrEmpty(fromVersionEntity.ComponentsBlobId))
            {
                comparison.ComponentChanges.Add("All components were removed");
            }
            else if (!string.IsNullOrEmpty(toVersionEntity.ComponentsBlobId))
            {
                comparison.ComponentChanges.Add("All components were added");
            }

            return comparison;
        }

        /// <summary>
        /// Gets version statistics for a report
        /// </summary>
        public async Task<VersionStatistics> GetVersionStatisticsAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version statistics for report {ReportId}", reportId);

            var versions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .OrderBy(rv => rv.VersionNumber)
                .ToListAsync(cancellationToken);

            if (!versions.Any())
            {
                throw new InvalidOperationException($"No versions found for report {reportId}");
            }

            var statistics = new VersionStatistics
            {
                TotalVersions = versions.Count,
                CurrentVersionNumber = versions.First(v => v.IsCurrent).VersionNumber,
                FirstVersionCreated = versions.First().CreatedAt,
                LastVersionCreated = versions.Last().CreatedAt,
                TotalStorageSize = versions.Sum(v => v.ComponentDataSize + v.JsonDataSize),
                VersionSizes = versions.ToDictionary(
                    v => v.VersionNumber,
                    v => v.ComponentDataSize + v.JsonDataSize)
            };

            return statistics;
        }

        /// <summary>
        /// Creates a version from existing data (for migration purposes)
        /// </summary>
        public async Task<ReportVersion> CreateVersionFromDataAsync(
            Guid reportId,
            CreateVersionRequest versionData,
            CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Creating version from data for report {ReportId}", reportId);

            // Get the report to get tenant ID
            var report = await _metadataRepository.GetReportAsync(reportId, cancellationToken);
            if (report == null)
            {
                throw new InvalidOperationException($"Report with ID {reportId} not found");
            }

            var version = new ReportVersion
            {
                ReportId = reportId,
                VersionNumber = versionData.VersionNumber,
                Description = versionData.Description,
                IsCurrent = versionData.IsCurrent
            };

            // Create version in SQL database first
            var createdVersion = await _metadataRepository.CreateVersionAsync(version, cancellationToken);

            // Create report data in Cosmos DB if we have data
            string? dataDocumentId = null;
            if (versionData.JsonData.Any())
            {
                var reportData = new ReportData
                {
                    Id = ReportData.CreateDocumentId(reportId, createdVersion.Id),
                    ReportId = reportId.ToString(),
                    VersionId = createdVersion.Id.ToString(),
                    TenantId = report.TenantId.ToString(),
                    PartitionKey = report.TenantId.ToString(),
                    Sections = ConvertDataToSections(versionData.JsonData)
                };

                dataDocumentId = await _dataRepository.CreateReportDataAsync(reportData, cancellationToken);
            }

            // Create components in Blob Storage
            string? componentsBlobId = null;
            if (versionData.Components.Any())
            {
                var componentDefinitions = versionData.Components.Select(c => new ComponentDefinition
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = c.SectionName,
                    SectionId = c.SectionId,
                    ComponentCode = c.ComponentCode,
                    TypeDefinitions = c.TypeDefinitions,
                    Imports = c.Imports,
                    Hash = CalculateComponentHash(c),
                    Framework = "NextJS",
                    StyleFramework = "TailwindCSS",
                    IsValid = true
                }).ToList();

                componentsBlobId = await _componentsRepository.SaveComponentsAsync(
                    reportId, createdVersion.Id, report.TenantId ?? Guid.Empty, componentDefinitions, cancellationToken);
            }

            // Update storage references in SQL
            await _metadataRepository.UpdateStorageReferencesAsync(
                createdVersion.Id, dataDocumentId, componentsBlobId, null, cancellationToken);

            _logger.LogInformation("Successfully created version {VersionNumber} from data for report {ReportId}",
                versionData.VersionNumber, reportId);

            return createdVersion;
        }

        /// <summary>
        /// Cleans up old versions based on retention policy
        /// </summary>
        public async Task<int> CleanupOldVersionsAsync(Guid reportId, int maxVersionsToKeep = 10, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Cleaning up old versions for report {ReportId}, keeping {MaxVersions} versions",
                reportId, maxVersionsToKeep);

            var versions = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .OrderByDescending(rv => rv.VersionNumber)
                .ToListAsync(cancellationToken);

            if (versions.Count <= maxVersionsToKeep)
            {
                return 0; // Nothing to clean up
            }

            var versionsToDelete = versions
                .Skip(maxVersionsToKeep)
                .Where(v => v.CanBeDeleted())
                .ToList();

            _context.ReportVersions.RemoveRange(versionsToDelete);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Cleaned up {DeletedCount} old versions for report {ReportId}",
                versionsToDelete.Count, reportId);

            return versionsToDelete.Count;
        }

        /// <summary>
        /// Converts component data to report sections
        /// </summary>
        private List<ReportSection> ConvertDataToSections(Dictionary<string, object> data)
        {
            var sections = new List<ReportSection>();
            int order = 0;

            foreach (var kvp in data)
            {
                sections.Add(new ReportSection
                {
                    Id = kvp.Key,
                    Name = kvp.Key,
                    Type = "data",
                    Order = order++,
                    Data = new Dictionary<string, object> { [kvp.Key] = kvp.Value }
                });
            }

            return sections;
        }

        /// <summary>
        /// Calculates hash for a component
        /// </summary>
        private string CalculateComponentHash(SectionComponent component)
        {
            var content = $"{component.SectionId}|{component.ComponentCode}|{component.TypeDefinitions}";
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(content));
            return Convert.ToHexString(hash);
        }
    }
}
