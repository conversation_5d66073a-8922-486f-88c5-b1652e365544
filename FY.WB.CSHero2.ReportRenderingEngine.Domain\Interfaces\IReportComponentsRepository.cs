using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for managing React components in Azure Blob Storage
    /// Handles ComponentsMetadata and individual ComponentDefinition files
    /// </summary>
    public interface IReportComponentsRepository
    {
        // Components Metadata Operations
        /// <summary>
        /// Gets components metadata for a report version
        /// </summary>
        Task<ComponentsMetadata?> GetComponentsMetadataAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets components metadata by blob ID
        /// </summary>
        Task<ComponentsMetadata?> GetComponentsMetadataAsync(string componentsBlobId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates components metadata file
        /// </summary>
        Task<string> CreateComponentsMetadataAsync(ComponentsMetadata metadata, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates components metadata file
        /// </summary>
        Task<string> UpdateComponentsMetadataAsync(ComponentsMetadata metadata, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes components metadata and all associated component files
        /// </summary>
        Task DeleteComponentsAsync(string componentsBlobId, CancellationToken cancellationToken = default);

        // Individual Component Operations
        /// <summary>
        /// Gets a specific component definition
        /// </summary>
        Task<ComponentDefinition?> GetComponentAsync(Guid reportId, Guid versionId, Guid tenantId, string sectionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all components for a report version
        /// </summary>
        Task<List<ComponentDefinition>> GetComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves a single component definition
        /// </summary>
        Task<string> SaveComponentAsync(Guid reportId, Guid versionId, Guid tenantId, ComponentDefinition component, CancellationToken cancellationToken = default);

        /// <summary>
        /// Saves multiple component definitions
        /// </summary>
        Task<string> SaveComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, List<ComponentDefinition> components, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a specific component
        /// </summary>
        Task DeleteComponentAsync(Guid reportId, Guid versionId, Guid tenantId, string sectionId, CancellationToken cancellationToken = default);

        // Bulk Operations
        /// <summary>
        /// Gets components for multiple report versions
        /// </summary>
        Task<Dictionary<Guid, List<ComponentDefinition>>> GetComponentsBulkAsync(List<(Guid reportId, Guid versionId, Guid tenantId)> requests, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes components for multiple report versions
        /// </summary>
        Task DeleteComponentsBulkAsync(List<string> componentsBlobIds, CancellationToken cancellationToken = default);

        // Asset Operations
        /// <summary>
        /// Saves component assets (images, files, etc.)
        /// </summary>
        Task<string> SaveAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, Stream assetStream, string contentType, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets component asset
        /// </summary>
        Task<Stream?> GetAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes component asset
        /// </summary>
        Task DeleteAssetAsync(Guid reportId, Guid versionId, Guid tenantId, string assetName, CancellationToken cancellationToken = default);

        /// <summary>
        /// Lists all assets for a report version
        /// </summary>
        Task<List<string>> ListAssetsAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default);

        // Migration Operations
        /// <summary>
        /// Creates components from legacy ComponentDefinition entities
        /// </summary>
        Task<string> CreateFromLegacyComponentsAsync(Guid reportId, Guid versionId, Guid tenantId, List<object> legacyComponents, CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrates components from SQL to Blob Storage
        /// </summary>
        Task<string> MigrateFromSqlAsync(Guid reportId, Guid versionId, Guid tenantId, string componentDataJson, CancellationToken cancellationToken = default);

        // Validation Operations
        /// <summary>
        /// Validates component structure and syntax
        /// </summary>
        Task<(bool isValid, List<string> errors)> ValidateComponentsAsync(List<ComponentDefinition> components, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets components size and metadata
        /// </summary>
        Task<(long size, DateTime lastModified, int componentCount)> GetComponentsInfoAsync(string componentsBlobId, CancellationToken cancellationToken = default);

        // Versioning Operations
        /// <summary>
        /// Copies components from one version to another
        /// </summary>
        Task<string> CopyComponentsAsync(string sourceComponentsBlobId, Guid newReportId, Guid newVersionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Compares components between two versions
        /// </summary>
        Task<List<string>> CompareComponentsAsync(string componentsBlobId1, string componentsBlobId2, CancellationToken cancellationToken = default);

        // Path Generation
        /// <summary>
        /// Generates blob path for components metadata
        /// </summary>
        string GenerateComponentsPath(Guid tenantId, Guid reportId, Guid versionId);

        /// <summary>
        /// Generates blob path for individual component
        /// </summary>
        string GenerateComponentPath(Guid tenantId, Guid reportId, Guid versionId, string sectionId);

        /// <summary>
        /// Generates blob path for component asset
        /// </summary>
        string GenerateAssetPath(Guid tenantId, Guid reportId, Guid versionId, string assetName);
    }
}
