using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for managing report data in Cosmos DB
    /// Handles ReportData documents with structured report content
    /// </summary>
    public interface IReportDataRepository
    {
        // Document Operations
        /// <summary>
        /// Gets report data document by ID
        /// </summary>
        Task<ReportData?> GetReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report data for a specific report version
        /// </summary>
        Task<ReportData?> GetReportDataAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new report data document
        /// </summary>
        Task<string> CreateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates an existing report data document
        /// </summary>
        Task<string> UpdateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a report data document
        /// </summary>
        Task DeleteReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Checks if a report data document exists
        /// </summary>
        Task<bool> ExistsAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);

        // Bulk Operations
        /// <summary>
        /// Gets multiple report data documents by IDs
        /// </summary>
        Task<List<ReportData>> GetReportDataBulkAsync(List<string> documentIds, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates multiple report data documents
        /// </summary>
        Task<List<string>> CreateReportDataBulkAsync(List<ReportData> reportDataList, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes multiple report data documents
        /// </summary>
        Task DeleteReportDataBulkAsync(List<string> documentIds, Guid tenantId, CancellationToken cancellationToken = default);

        // Query Operations
        /// <summary>
        /// Gets all report data documents for a tenant
        /// </summary>
        Task<List<ReportData>> GetReportDataForTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets report data documents for a specific report (all versions)
        /// </summary>
        Task<List<ReportData>> GetReportDataForReportAsync(Guid reportId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches report data documents by content
        /// </summary>
        Task<List<ReportData>> SearchReportDataAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default);

        // Migration Operations
        /// <summary>
        /// Creates report data from legacy JSON data
        /// </summary>
        Task<string> CreateFromLegacyDataAsync(Guid reportId, Guid versionId, Guid tenantId, Dictionary<string, object> jsonData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Migrates report data from SQL JSON fields to Cosmos DB
        /// </summary>
        Task<string> MigrateFromSqlAsync(Guid reportId, Guid versionId, Guid tenantId, string jsonData, CancellationToken cancellationToken = default);

        // Validation Operations
        /// <summary>
        /// Validates report data structure
        /// </summary>
        Task<(bool isValid, List<string> errors)> ValidateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets document size and metadata
        /// </summary>
        Task<(long size, DateTime lastModified, Dictionary<string, object> metadata)> GetDocumentInfoAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);

        // Versioning Operations
        /// <summary>
        /// Creates a copy of report data for a new version
        /// </summary>
        Task<string> CopyReportDataAsync(string sourceDocumentId, Guid newReportId, Guid newVersionId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Compares two report data documents
        /// </summary>
        Task<List<string>> CompareReportDataAsync(string documentId1, string documentId2, Guid tenantId, CancellationToken cancellationToken = default);
    }
}
