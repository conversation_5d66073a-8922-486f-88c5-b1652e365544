using FY.WB.CSHero2.Domain.Entities;

namespace FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces
{
    /// <summary>
    /// Repository interface for managing report metadata in SQL Database
    /// Handles Report and ReportVersion entities with multi-storage references
    /// </summary>
    public interface IReportMetadataRepository
    {
        // Report Operations
        /// <summary>
        /// Gets a report by ID with basic metadata
        /// </summary>
        Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a report by ID for a specific tenant
        /// </summary>
        Task<Report?> GetReportAsync(Guid reportId, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new report with metadata
        /// </summary>
        Task<Report> CreateReportAsync(Report report, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates report metadata
        /// </summary>
        Task<Report> UpdateReportAsync(Report report, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a report and all its versions
        /// </summary>
        Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets all reports for a tenant
        /// </summary>
        Task<List<Report>> GetReportsForTenantAsync(Guid tenantId, CancellationToken cancellationToken = default);

        // ReportVersion Operations
        /// <summary>
        /// Gets a specific version of a report
        /// </summary>
        Task<ReportVersion?> GetVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a version by its ID
        /// </summary>
        Task<ReportVersion?> GetVersionByIdAsync(Guid versionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current version of a report
        /// </summary>
        Task<ReportVersion?> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets version history for a report
        /// </summary>
        Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a new version of a report
        /// </summary>
        Task<ReportVersion> CreateVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates version metadata (including storage references)
        /// </summary>
        Task<ReportVersion> UpdateVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a specific version
        /// </summary>
        Task DeleteVersionAsync(Guid versionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets the current version for a report
        /// </summary>
        Task SetCurrentVersionAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the next version number for a report
        /// </summary>
        Task<int> GetNextVersionNumberAsync(Guid reportId, CancellationToken cancellationToken = default);

        // Multi-Storage Reference Operations
        /// <summary>
        /// Updates storage references for a version (DataDocumentId, ComponentsBlobId, etc.)
        /// </summary>
        Task UpdateStorageReferencesAsync(Guid versionId, string? dataDocumentId, string? componentsBlobId, string? stylesBlobId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets versions that need migration to multi-storage
        /// </summary>
        Task<List<ReportVersion>> GetVersionsForMigrationAsync(string fromStrategy = "SQL", CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates storage strategy for a version
        /// </summary>
        Task UpdateStorageStrategyAsync(Guid versionId, string storageStrategy, CancellationToken cancellationToken = default);

        // Bulk Operations
        /// <summary>
        /// Gets multiple versions by their IDs
        /// </summary>
        Task<List<ReportVersion>> GetVersionsByIdsAsync(List<Guid> versionIds, CancellationToken cancellationToken = default);

        /// <summary>
        /// Bulk update storage references for multiple versions
        /// </summary>
        Task BulkUpdateStorageReferencesAsync(Dictionary<Guid, (string? dataDocumentId, string? componentsBlobId, string? stylesBlobId)> updates, CancellationToken cancellationToken = default);
    }
}
