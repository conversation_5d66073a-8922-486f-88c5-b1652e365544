using System.Collections.Generic;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration
{
    /// <summary>
    /// Configuration options for CosmosDB
    /// </summary>
    public class CosmosDbOptions
    {
        /// <summary>
        /// Configuration section name
        /// </summary>
        public const string SectionName = "CosmosDb";

        /// <summary>
        /// Connection string for CosmosDB
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Database name
        /// </summary>
        public string DatabaseName { get; set; } = string.Empty;

        /// <summary>
        /// Container configurations
        /// </summary>
        public ContainerOptions Containers { get; set; } = new();

        /// <summary>
        /// Maximum retry attempts
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int RequestTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Maximum connections
        /// </summary>
        public int MaxConnections { get; set; } = 50;

        /// <summary>
        /// Report data container name (convenience property)
        /// </summary>
        public string ReportDataContainerName => Containers.ReportData;
    }

    /// <summary>
    /// Container configuration options
    /// </summary>
    public class ContainerOptions
    {
        /// <summary>
        /// Report styles container name
        /// </summary>
        public string ReportStyles { get; set; } = "report-styles";

        /// <summary>
        /// Report data container name
        /// </summary>
        public string ReportData { get; set; } = "report-data";
    }
}