using FY.WB.CSHero2.ReportRenderingEngine.Domain.Models;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for managing report data in Cosmos DB
    /// </summary>
    public class ReportDataRepository : IReportDataRepository
    {
        private readonly Container _container;
        private readonly ILogger<ReportDataRepository> _logger;
        private readonly CosmosDbOptions _options;

        public ReportDataRepository(CosmosClient cosmosClient, IOptions<CosmosDbOptions> options, ILogger<ReportDataRepository> logger)
        {
            _options = options.Value;
            _logger = logger;
            _container = cosmosClient.GetContainer(_options.DatabaseName, _options.ReportDataContainerName);
        }

        // Document Operations
        public async Task<ReportData?> GetReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data document {DocumentId} for tenant {TenantId}", documentId, tenantId);
                
                var response = await _container.ReadItemAsync<ReportData>(
                    documentId,
                    new PartitionKey(tenantId.ToString()),
                    cancellationToken: cancellationToken);
                
                _logger.LogInformation("Retrieved report data document {DocumentId}", documentId);
                return response.Resource;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Report data document {DocumentId} not found", documentId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data document {DocumentId}", documentId);
                throw;
            }
        }

        public async Task<ReportData?> GetReportDataAsync(Guid reportId, Guid versionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var documentId = ReportData.CreateDocumentId(reportId, versionId);
            return await GetReportDataAsync(documentId, tenantId, cancellationToken);
        }

        public async Task<string> CreateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Creating report data document {DocumentId} for tenant {TenantId}", reportData.Id, reportData.TenantId);
                
                // Ensure partition key is set
                reportData.PartitionKey = reportData.TenantId;
                reportData.CreatedAt = DateTime.UtcNow;
                reportData.LastModified = DateTime.UtcNow;
                
                var response = await _container.CreateItemAsync(
                    reportData,
                    new PartitionKey(reportData.PartitionKey),
                    cancellationToken: cancellationToken);
                
                _logger.LogInformation("Created report data document {DocumentId}", reportData.Id);
                return response.Resource.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating report data document {DocumentId}", reportData.Id);
                throw;
            }
        }

        public async Task<string> UpdateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Updating report data document {DocumentId}", reportData.Id);
                
                reportData.LastModified = DateTime.UtcNow;
                
                var response = await _container.UpsertItemAsync(
                    reportData,
                    new PartitionKey(reportData.PartitionKey),
                    cancellationToken: cancellationToken);
                
                _logger.LogInformation("Updated report data document {DocumentId}", reportData.Id);
                return response.Resource.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating report data document {DocumentId}", reportData.Id);
                throw;
            }
        }

        public async Task DeleteReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Deleting report data document {DocumentId}", documentId);
                
                await _container.DeleteItemAsync<ReportData>(
                    documentId,
                    new PartitionKey(tenantId.ToString()),
                    cancellationToken: cancellationToken);
                
                _logger.LogInformation("Deleted report data document {DocumentId}", documentId);
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("Report data document {DocumentId} not found for deletion", documentId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting report data document {DocumentId}", documentId);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                await _container.ReadItemAsync<ReportData>(
                    documentId,
                    new PartitionKey(tenantId.ToString()),
                    cancellationToken: cancellationToken);
                return true;
            }
            catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
        }

        // Bulk Operations
        public async Task<List<ReportData>> GetReportDataBulkAsync(List<string> documentIds, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting {Count} report data documents for tenant {TenantId}", documentIds.Count, tenantId);
            
            var results = new List<ReportData>();
            var tasks = documentIds.Select(id => GetReportDataAsync(id, tenantId, cancellationToken));
            var documents = await Task.WhenAll(tasks);
            
            results.AddRange(documents.Where(d => d != null)!);
            return results;
        }

        public async Task<List<string>> CreateReportDataBulkAsync(List<ReportData> reportDataList, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Creating {Count} report data documents", reportDataList.Count);
            
            var results = new List<string>();
            var tasks = reportDataList.Select(data => CreateReportDataAsync(data, cancellationToken));
            var documentIds = await Task.WhenAll(tasks);
            
            results.AddRange(documentIds);
            return results;
        }

        public async Task DeleteReportDataBulkAsync(List<string> documentIds, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Deleting {Count} report data documents", documentIds.Count);
            
            var tasks = documentIds.Select(id => DeleteReportDataAsync(id, tenantId, cancellationToken));
            await Task.WhenAll(tasks);
        }

        // Query Operations
        public async Task<List<ReportData>> GetReportDataForTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting all report data for tenant {TenantId}", tenantId);
                
                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId")
                    .WithParameter("@tenantId", tenantId.ToString());
                
                var results = new List<ReportData>();
                using var feedIterator = _container.GetItemQueryIterator<ReportData>(queryDefinition);
                
                while (feedIterator.HasMoreResults)
                {
                    var response = await feedIterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }
                
                _logger.LogInformation("Retrieved {Count} report data documents for tenant {TenantId}", results.Count, tenantId);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data for tenant {TenantId}", tenantId);
                throw;
            }
        }

        public async Task<List<ReportData>> GetReportDataForReportAsync(Guid reportId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Getting report data for report {ReportId} in tenant {TenantId}", reportId, tenantId);
                
                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId AND c.reportId = @reportId")
                    .WithParameter("@tenantId", tenantId.ToString())
                    .WithParameter("@reportId", reportId.ToString());
                
                var results = new List<ReportData>();
                using var feedIterator = _container.GetItemQueryIterator<ReportData>(queryDefinition);
                
                while (feedIterator.HasMoreResults)
                {
                    var response = await feedIterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }
                
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting report data for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task<List<ReportData>> SearchReportDataAsync(Guid tenantId, string searchTerm, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Searching report data for tenant {TenantId} with term {SearchTerm}", tenantId, searchTerm);
                
                var queryDefinition = new QueryDefinition(
                    "SELECT * FROM c WHERE c.partitionKey = @tenantId AND CONTAINS(LOWER(c.metadata.title), LOWER(@searchTerm))")
                    .WithParameter("@tenantId", tenantId.ToString())
                    .WithParameter("@searchTerm", searchTerm);
                
                var results = new List<ReportData>();
                using var feedIterator = _container.GetItemQueryIterator<ReportData>(queryDefinition);
                
                while (feedIterator.HasMoreResults)
                {
                    var response = await feedIterator.ReadNextAsync(cancellationToken);
                    results.AddRange(response);
                }
                
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching report data for tenant {TenantId}", tenantId);
                throw;
            }
        }

        // Migration Operations
        public async Task<string> CreateFromLegacyDataAsync(Guid reportId, Guid versionId, Guid tenantId, Dictionary<string, object> jsonData, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Creating report data from legacy data for report {ReportId} version {VersionId}", reportId, versionId);
            
            var reportData = new ReportData
            {
                Id = ReportData.CreateDocumentId(reportId, versionId),
                ReportId = reportId.ToString(),
                VersionId = versionId.ToString(),
                TenantId = tenantId.ToString(),
                PartitionKey = tenantId.ToString()
            };
            
            // Convert legacy JSON data to structured sections
            // This is a simplified conversion - you may need to enhance based on your data structure
            if (jsonData.ContainsKey("sections"))
            {
                var sectionsJson = JsonSerializer.Serialize(jsonData["sections"]);
                reportData.Sections = JsonSerializer.Deserialize<List<ReportSection>>(sectionsJson) ?? new List<ReportSection>();
            }
            
            return await CreateReportDataAsync(reportData, cancellationToken);
        }

        public async Task<string> MigrateFromSqlAsync(Guid reportId, Guid versionId, Guid tenantId, string jsonData, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Migrating report data from SQL for report {ReportId} version {VersionId}", reportId, versionId);
            
            var legacyData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonData) ?? new Dictionary<string, object>();
            return await CreateFromLegacyDataAsync(reportId, versionId, tenantId, legacyData, cancellationToken);
        }

        // Validation Operations
        public async Task<(bool isValid, List<string> errors)> ValidateReportDataAsync(ReportData reportData, CancellationToken cancellationToken = default)
        {
            var errors = new List<string>();
            
            if (string.IsNullOrEmpty(reportData.Id))
                errors.Add("Document ID is required");
            
            if (string.IsNullOrEmpty(reportData.ReportId))
                errors.Add("Report ID is required");
            
            if (string.IsNullOrEmpty(reportData.VersionId))
                errors.Add("Version ID is required");
            
            if (string.IsNullOrEmpty(reportData.TenantId))
                errors.Add("Tenant ID is required");
            
            return (errors.Count == 0, errors);
        }

        public async Task<(long size, DateTime lastModified, Dictionary<string, object> metadata)> GetDocumentInfoAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var document = await GetReportDataAsync(documentId, tenantId, cancellationToken);
            if (document == null)
                throw new InvalidOperationException($"Document {documentId} not found");
            
            var serialized = JsonSerializer.Serialize(document);
            var size = System.Text.Encoding.UTF8.GetByteCount(serialized);
            
            var metadata = new Dictionary<string, object>
            {
                ["sectionCount"] = document.Sections.Count,
                ["reportId"] = document.ReportId,
                ["versionId"] = document.VersionId
            };
            
            return (size, document.LastModified, metadata);
        }

        // Versioning Operations
        public async Task<string> CopyReportDataAsync(string sourceDocumentId, Guid newReportId, Guid newVersionId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Copying report data from {SourceDocumentId} to new version", sourceDocumentId);
            
            var sourceData = await GetReportDataAsync(sourceDocumentId, tenantId, cancellationToken);
            if (sourceData == null)
                throw new InvalidOperationException($"Source document {sourceDocumentId} not found");
            
            var newData = new ReportData
            {
                Id = ReportData.CreateDocumentId(newReportId, newVersionId),
                ReportId = newReportId.ToString(),
                VersionId = newVersionId.ToString(),
                TenantId = tenantId.ToString(),
                PartitionKey = tenantId.ToString(),
                Sections = sourceData.Sections.Select(s => new ReportSection
                {
                    Id = s.Id,
                    Name = s.Name,
                    Type = s.Type,
                    Data = s.Data,
                    Order = s.Order
                }).ToList()
            };
            
            return await CreateReportDataAsync(newData, cancellationToken);
        }

        public async Task<List<string>> CompareReportDataAsync(string documentId1, string documentId2, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var doc1 = await GetReportDataAsync(documentId1, tenantId, cancellationToken);
            var doc2 = await GetReportDataAsync(documentId2, tenantId, cancellationToken);
            
            var differences = new List<string>();
            
            if (doc1 == null || doc2 == null)
            {
                differences.Add("One or both documents not found");
                return differences;
            }
            
            if (doc1.Sections.Count != doc2.Sections.Count)
                differences.Add($"Section count differs: {doc1.Sections.Count} vs {doc2.Sections.Count}");
            
            // Add more detailed comparison logic as needed
            
            return differences;
        }
    }
}
