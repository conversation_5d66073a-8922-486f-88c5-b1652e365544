using FY.WB.CSHero2.Domain.Entities;
using FY.WB.CSHero2.Infrastructure.Persistence;
using FY.WB.CSHero2.ReportRenderingEngine.Domain.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Repositories
{
    /// <summary>
    /// Repository implementation for managing report metadata in SQL Database
    /// </summary>
    public class ReportMetadataRepository : IReportMetadataRepository
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ReportMetadataRepository> _logger;

        public ReportMetadataRepository(ApplicationDbContext context, ILogger<ReportMetadataRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        // Report Operations
        public async Task<Report?> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting report {ReportId}", reportId);
            return await _context.Reports.FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
        }

        public async Task<Report?> GetReportAsync(Guid reportId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting report {ReportId} for tenant {TenantId}", reportId, tenantId);
            return await _context.Reports.FirstOrDefaultAsync(r => r.Id == reportId && r.TenantId == tenantId, cancellationToken);
        }

        public async Task<Report> CreateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Creating report {ReportId} for tenant {TenantId}", report.Id, report.TenantId);
            
            _context.Reports.Add(report);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Created report {ReportId}", report.Id);
            return report;
        }

        public async Task<Report> UpdateReportAsync(Report report, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Updating report {ReportId}", report.Id);
            
            _context.Reports.Update(report);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Updated report {ReportId}", report.Id);
            return report;
        }

        public async Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Deleting report {ReportId}", reportId);
            
            var report = await _context.Reports.FirstOrDefaultAsync(r => r.Id == reportId, cancellationToken);
            if (report != null)
            {
                _context.Reports.Remove(report);
                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Deleted report {ReportId}", reportId);
            }
        }

        public async Task<List<Report>> GetReportsForTenantAsync(Guid tenantId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting reports for tenant {TenantId}", tenantId);
            return await _context.Reports.Where(r => r.TenantId == tenantId).ToListAsync(cancellationToken);
        }

        // ReportVersion Operations
        public async Task<ReportVersion?> GetVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version {VersionNumber} for report {ReportId}", versionNumber, reportId);
            return await _context.ReportVersions
                .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.VersionNumber == versionNumber, cancellationToken);
        }

        public async Task<ReportVersion?> GetVersionByIdAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version {VersionId}", versionId);
            return await _context.ReportVersions.FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);
        }

        public async Task<ReportVersion?> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting current version for report {ReportId}", reportId);
            return await _context.ReportVersions
                .FirstOrDefaultAsync(rv => rv.ReportId == reportId && rv.IsCurrent, cancellationToken);
        }

        public async Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting version history for report {ReportId}", reportId);
            return await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .OrderBy(rv => rv.VersionNumber)
                .ToListAsync(cancellationToken);
        }

        public async Task<ReportVersion> CreateVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Creating version {VersionNumber} for report {ReportId}", version.VersionNumber, version.ReportId);
            
            _context.ReportVersions.Add(version);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Created version {VersionId}", version.Id);
            return version;
        }

        public async Task<ReportVersion> UpdateVersionAsync(ReportVersion version, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Updating version {VersionId}", version.Id);
            
            _context.ReportVersions.Update(version);
            await _context.SaveChangesAsync(cancellationToken);
            
            _logger.LogInformation("Updated version {VersionId}", version.Id);
            return version;
        }

        public async Task DeleteVersionAsync(Guid versionId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Deleting version {VersionId}", versionId);
            
            var version = await _context.ReportVersions.FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);
            if (version != null)
            {
                _context.ReportVersions.Remove(version);
                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Deleted version {VersionId}", versionId);
            }
        }

        public async Task SetCurrentVersionAsync(Guid reportId, Guid versionId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Setting current version {VersionId} for report {ReportId}", versionId, reportId);
            
            // Clear current flag from all versions
            var versions = await _context.ReportVersions.Where(rv => rv.ReportId == reportId).ToListAsync(cancellationToken);
            foreach (var version in versions)
            {
                version.IsCurrent = version.Id == versionId;
            }
            
            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Set current version {VersionId} for report {ReportId}", versionId, reportId);
        }

        public async Task<int> GetNextVersionNumberAsync(Guid reportId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting next version number for report {ReportId}", reportId);
            
            var maxVersion = await _context.ReportVersions
                .Where(rv => rv.ReportId == reportId)
                .MaxAsync(rv => (int?)rv.VersionNumber, cancellationToken);
            
            return (maxVersion ?? 0) + 1;
        }

        // Multi-Storage Reference Operations
        public async Task UpdateStorageReferencesAsync(Guid versionId, string? dataDocumentId, string? componentsBlobId, string? stylesBlobId, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Updating storage references for version {VersionId}", versionId);
            
            var version = await _context.ReportVersions.FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);
            if (version != null)
            {
                version.DataDocumentId = dataDocumentId;
                version.ComponentsBlobId = componentsBlobId;
                version.StylesBlobId = stylesBlobId;
                version.StorageStrategy = "MultiStorage";
                
                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Updated storage references for version {VersionId}", versionId);
            }
        }

        public async Task<List<ReportVersion>> GetVersionsForMigrationAsync(string fromStrategy = "SQL", CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting versions for migration from strategy {FromStrategy}", fromStrategy);
            return await _context.ReportVersions
                .Where(rv => rv.StorageStrategy == fromStrategy)
                .ToListAsync(cancellationToken);
        }

        public async Task UpdateStorageStrategyAsync(Guid versionId, string storageStrategy, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Updating storage strategy for version {VersionId} to {StorageStrategy}", versionId, storageStrategy);
            
            var version = await _context.ReportVersions.FirstOrDefaultAsync(rv => rv.Id == versionId, cancellationToken);
            if (version != null)
            {
                version.StorageStrategy = storageStrategy;
                await _context.SaveChangesAsync(cancellationToken);
                _logger.LogInformation("Updated storage strategy for version {VersionId}", versionId);
            }
        }

        // Bulk Operations
        public async Task<List<ReportVersion>> GetVersionsByIdsAsync(List<Guid> versionIds, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Getting {Count} versions by IDs", versionIds.Count);
            return await _context.ReportVersions
                .Where(rv => versionIds.Contains(rv.Id))
                .ToListAsync(cancellationToken);
        }

        public async Task BulkUpdateStorageReferencesAsync(Dictionary<Guid, (string? dataDocumentId, string? componentsBlobId, string? stylesBlobId)> updates, CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Bulk updating storage references for {Count} versions", updates.Count);
            
            var versionIds = updates.Keys.ToList();
            var versions = await _context.ReportVersions
                .Where(rv => versionIds.Contains(rv.Id))
                .ToListAsync(cancellationToken);
            
            foreach (var version in versions)
            {
                if (updates.TryGetValue(version.Id, out var refs))
                {
                    version.DataDocumentId = refs.dataDocumentId;
                    version.ComponentsBlobId = refs.componentsBlobId;
                    version.StylesBlobId = refs.stylesBlobId;
                    version.StorageStrategy = "MultiStorage";
                }
            }
            
            await _context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Bulk updated storage references for {Count} versions", updates.Count);
        }
    }
}
