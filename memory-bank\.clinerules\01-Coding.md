## Token Management

Monitor token usage continuously. When approaching 160,000 tokens in current task:
- Create detailed handoff document with current progress, next steps, and context
- Start new task to prevent context window overflow
- Maintain continuity through comprehensive handoff notes

## Fallback Plan

This is a development environment with test data from the SeedData files. No fallback plans are necessary.
- Project is version controlled through GitHub
- Sql DB, Cosmos DB, Blob Storage can be deleted and recreated at any time
- All backup files, if any, should be stored in the directory `FY.WB.CSHERO2\backups`

