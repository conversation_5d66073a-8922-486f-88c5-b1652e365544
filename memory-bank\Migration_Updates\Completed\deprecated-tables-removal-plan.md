# Deprecated Tables Removal Plan

## Overview
This document provides a comprehensive plan to remove the deprecated tables (`Invoices`, `Forms`, `Uploads`) from the FY.WB.CSHero2 project. These tables were included in the migration consolidation but are no longer needed for the application.

## Current State Analysis

### Deprecated Tables in Database
1. **`Invoices`** - Billing management (deprecated)
2. **`Forms`** - Customer interaction forms (deprecated) 
3. **`Uploads`** - File management system (deprecated)

### Code Dependencies Found
- **Domain Entities**: `FY.WB.CSHero2.Domain/Entities/Invoice.cs`, `Form.cs`, `Upload.cs`
- **Entity Configurations**: `InvoiceConfiguration.cs`, `FormConfiguration.cs`, `UploadConfiguration.cs`
- **DbContext DbSets**: `DbSet<Invoice> Invoices`, `DbSet<Form> Forms`, `DbSet<Upload> Uploads`
- **TenantProfile Navigation Properties**: `Forms` and `Invoices` collections
- **Seeder Comments**: Already marked as deprecated in `SqlSeeder.cs` and `DataSeeder.cs`

## Implementation Plan

### Phase 1: Create Migration to Drop Tables

#### Step 1: Generate Migration
```bash
# Navigate to project root
cd C:\Users\<USER>\Documents\GitHub\ChildrensVillage\FY.WB.CSHero2

# Create new migration
dotnet ef migrations add RemoveDeprecatedTables --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
```

#### Step 2: Migration Content Template
The generated migration should be modified to include:

```csharp
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveDeprecatedTables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop indexes first (dependency order)
            migrationBuilder.DropIndex(
                name: "IX_Forms_Category",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Category_Priority_Date", 
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Date",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Email",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_Priority",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Forms_TenantId",
                table: "Forms");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_OrderNumber",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_TenantId",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_ContentType",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename_StoragePath",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_Filename_StorageProvider",
                table: "Uploads");

            migrationBuilder.DropIndex(
                name: "IX_Uploads_StorageProvider",
                table: "Uploads");

            // Drop tables (no dependencies between these deprecated tables)
            migrationBuilder.DropTable(
                name: "Forms");

            migrationBuilder.DropTable(
                name: "Invoices");

            migrationBuilder.DropTable(
                name: "Uploads");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Recreate tables if rollback needed
            // Note: This would recreate the deprecated tables
            // Consider if rollback is actually needed for deprecated functionality
            
            // For safety, include table recreation code here
            // (Copy from original 20250604004236_01_Foundation.cs migration)
            
            // Forms table recreation
            migrationBuilder.CreateTable(
                name: "Forms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CustomerName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Priority = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Forms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Forms_TenantProfiles_TenantId",
                        column: x => x.TenantId,
                        principalTable: "TenantProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Invoices table recreation
            migrationBuilder.CreateTable(
                name: "Invoices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OrderNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Plans = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Amount = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Invoices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Invoices_TenantProfiles_TenantId",
                        column: x => x.TenantId,
                        principalTable: "TenantProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            // Uploads table recreation
            migrationBuilder.CreateTable(
                name: "Uploads",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Filename = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Size = table.Column<long>(type: "bigint", nullable: false),
                    ContentType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    StoragePath = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    StorageProvider = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    ExternalUrl = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Checksum = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Uploads", x => x.Id);
                    table.CheckConstraint("CK_Upload_Size", "[Size] >= 0");
                });

            // Recreate indexes
            // (Include all index creation code from original migration)
        }
    }
}
```

### Phase 2: Remove Code Dependencies

#### Step 1: Remove Domain Entities
```bash
# Remove domain entity files
Remove-Item "FY.WB.CSHero2.Domain\Entities\Invoice.cs"
Remove-Item "FY.WB.CSHero2.Domain\Entities\Form.cs" 
Remove-Item "FY.WB.CSHero2.Domain\Entities\Upload.cs"
```

#### Step 2: Remove Entity Configurations
```bash
# Remove configuration files
Remove-Item "FY.WB.CSHero2.Infrastructure\Persistence\Configurations\InvoiceConfiguration.cs"
Remove-Item "FY.WB.CSHero2.Infrastructure\Persistence\Configurations\FormConfiguration.cs"
Remove-Item "FY.WB.CSHero2.Infrastructure\Persistence\Configurations\UploadConfiguration.cs"
```

#### Step 3: Update ApplicationDbContext
Remove these lines from `ApplicationDbContext.cs`:
```csharp
// Remove these DbSet declarations
public DbSet<Invoice> Invoices { get; set; } = null!;
public DbSet<Form> Forms { get; set; } = null!;
public DbSet<Upload> Uploads { get; set; } = null!;
```

#### Step 4: Update TenantProfile Entity
Remove navigation properties from `TenantProfile.cs`:
```csharp
// Remove these navigation properties
public virtual ICollection<Form> Forms { get; set; } = new List<Form>();
public virtual ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
```

#### Step 5: Clean Up Seeder Comments
Update `SqlSeeder.cs` and `DataSeeder.cs` to remove the deprecated entity comments since the tables will no longer exist.

### Phase 3: Testing and Validation

#### Step 1: Build Validation
```bash
# Test compilation
dotnet build FY.WB.CSHero2.Infrastructure
dotnet build FY.WB.CSHero2
```

#### Step 2: Migration Testing
```bash
# Test migration on development database
dotnet ef database update --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2
```

#### Step 3: Application Testing
```bash
# Run application tests
dotnet test

# Test application startup
dotnet run --project FY.WB.CSHero2
```

#### Step 4: Database Validation
```sql
-- Verify tables are removed
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME IN ('Forms', 'Invoices', 'Uploads');
-- Should return no results

-- Verify no orphaned foreign keys
SELECT 
    OBJECT_NAME(f.parent_object_id) AS TableName,
    COL_NAME(fc.parent_object_id, fc.parent_column_id) AS ColumnName,
    OBJECT_NAME(f.referenced_object_id) AS ReferencedTableName,
    COL_NAME(fc.referenced_object_id, fc.referenced_column_id) AS ReferencedColumnName
FROM sys.foreign_keys AS f
INNER JOIN sys.foreign_key_columns AS fc ON f.object_id = fc.constraint_object_id
WHERE OBJECT_NAME(f.referenced_object_id) IN ('Forms', 'Invoices', 'Uploads')
   OR OBJECT_NAME(f.parent_object_id) IN ('Forms', 'Invoices', 'Uploads');
-- Should return no results
```

## Risk Assessment

### Low Risk
- **No Data Loss Concern**: Tables are deprecated and not used
- **No Business Impact**: Functionality already removed from application
- **Clean Dependencies**: No other tables depend on these deprecated tables

### Mitigation Strategies
- **Backup**: Create database backup before migration (standard practice)
- **Rollback**: Migration includes Down() method to recreate tables if needed
- **Testing**: Comprehensive testing in development environment first

## Benefits

### Database Cleanup
- **Reduced Schema Complexity**: Remove unused tables and indexes
- **Improved Performance**: Fewer tables to maintain and backup
- **Cleaner Migrations**: Remove deprecated elements from future migrations

### Code Cleanup  
- **Reduced Maintenance**: Remove unused entity configurations and domain models
- **Cleaner Architecture**: Remove deprecated navigation properties
- **Simplified DbContext**: Fewer DbSets to manage

## Implementation Timeline

| Phase | Duration | Tasks |
|-------|----------|-------|
| Phase 1 | 1 hour | Create and customize migration |
| Phase 2 | 1 hour | Remove code dependencies |
| Phase 3 | 2 hours | Testing and validation |
| **Total** | **4 hours** | **Complete cleanup** |

## Success Criteria

- [ ] Migration applies successfully without errors
- [ ] Application builds and runs without compilation errors
- [ ] All tests pass
- [ ] Database no longer contains deprecated tables
- [ ] No orphaned foreign key references
- [ ] Application functionality remains intact

## Next Steps

1. **Review and Approve**: Confirm this plan meets requirements
2. **Execute Phase 1**: Create the migration
3. **Execute Phase 2**: Remove code dependencies  
4. **Execute Phase 3**: Test and validate
5. **Deploy**: Apply to staging/production environments

---

**Note**: This is a development environment cleanup with no rollback requirements as specified. The migration includes rollback capability for safety, but it's not expected to be needed since these are deprecated tables.