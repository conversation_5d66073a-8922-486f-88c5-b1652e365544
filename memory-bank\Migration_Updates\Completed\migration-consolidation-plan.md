# Migration Consolidation Implementation Plan

## Phase 1: Backup and Preparation

### 1.1 Create Migration Backup
```bash
# Create backup directory
mkdir -p FY.WB.CSHero2.Infrastructure/Migrations/Backup/$(date +%Y%m%d)

# Backup existing migrations
cp FY.WB.CSHero2.Infrastructure/Migrations/*.cs FY.WB.CSHero2.Infrastructure/Migrations/Backup/$(date +%Y%m%d)/
```

### 1.2 Database State Verification
```bash
# Generate current model snapshot for comparison
dotnet ef migrations script --output current-schema.sql
```

### 1.3 Seeding Data Analysis
- Verify all seed data files are consistent with current schema
- Document current entity relationships
- Validate foreign key constraints in seed data

## Phase 2: Create Consolidated Migrations

### 2.1 Foundation Migration (01_Foundation)
**Target**: Core application infrastructure aligned with seeding architecture

**Entities**:
- AspNet Identity tables
- TenantProfiles (root entity for multi-tenancy)
- ApplicationUsers (depends on TenantProfiles)
- Clients (depends on TenantProfiles)
- Templates (independent, enhanced for multi-storage)

**Key Features**:
- Proper tenant isolation
- Enhanced template structure for multi-storage
- Optimized indexes for performance
- Audit trail support

### 2.2 Report Structure Migration (02_ReportStructure)
**Target**: Complete report entity ecosystem

**Entities**:
- Reports (enhanced with multi-storage references)
- ReportVersions (with Cosmos DB and Blob Storage integration)
- ReportSections (structured content organization)
- ReportSectionFields (granular data management)

**Key Features**:
- Multi-storage architecture support
- Version management
- Hierarchical section/field structure
- Cross-storage ID references

### 2.3 Multi-Storage Integration Migration (03_MultiStorageIntegration)
**Target**: Advanced rendering and storage capabilities

**Entities**:
- ComponentDefinitions (React component management)
- ReportStyles (styling and theming)
- Storage reference columns across entities

**Key Features**:
- Cosmos DB document references
- Blob Storage component paths
- Style management system
- Component generation tracking

### 2.4 Supporting Entities Migration (04_SupportingEntities)
**Target**: Business process support

**Entities**:
- Forms (customer interaction)
- Invoices (billing management)
- Uploads (file management)

**Key Features**:
- Business process support
- File management capabilities
- Billing integration

## Phase 3: Migration Generation Commands

### 3.1 Remove Existing Migrations
```bash
# Remove existing migration files (keep backup)
rm FY.WB.CSHero2.Infrastructure/Migrations/20250522172751_Initial.*
rm FY.WB.CSHero2.Infrastructure/Migrations/20250526220454_AddReportRenderingV2EntitiesPhase1.*
rm FY.WB.CSHero2.Infrastructure/Migrations/20250602210411_AddMultiStorageSupport.*
rm FY.WB.CSHero2.Infrastructure/Migrations/20250602224758_AddReportSectionEntities.*
```

### 3.2 Generate New Consolidated Migrations
```bash
# Generate foundation migration
dotnet ef migrations add 01_Foundation --output-dir Migrations

# Generate report structure migration
dotnet ef migrations add 02_ReportStructure --output-dir Migrations

# Generate multi-storage integration migration
dotnet ef migrations add 03_MultiStorageIntegration --output-dir Migrations

# Generate supporting entities migration
dotnet ef migrations add 04_SupportingEntities --output-dir Migrations
```

## Phase 4: Seeding Integration

### 4.1 Update DataSeeder for Consolidated Structure
- Align seeding order with new migration structure
- Implement proper dependency management
- Add cross-storage coordination
- Enhance error handling and rollback

### 4.2 Seeding Order Alignment
```
01_Foundation:
  1. TenantProfiles
  2. ApplicationUsers
  3. Clients
  4. Templates

02_ReportStructure:
  5. Reports
  6. ReportVersions
  7. ReportSections
  8. ReportSectionFields

03_MultiStorageIntegration:
  9. ReportStyles
  10. ComponentDefinitions
  11. Cross-storage references

04_SupportingEntities:
  12. Forms
  13. Invoices
  14. Uploads
```

## Phase 5: Testing and Validation

### 5.1 Fresh Database Testing
```bash
# Test on fresh database
dotnet ef database drop --force
dotnet ef database update
```

### 5.2 Existing Database Migration Testing
```bash
# Test migration from current state
dotnet ef migrations script --from 20250602224758_AddReportSectionEntities --output migration-test.sql
```

### 5.3 Seeding Validation
```bash
# Run seeding process
dotnet run --project FY.WB.CSHero2 -- seed-data
```

### 5.4 Data Integrity Checks
- Verify all foreign key relationships
- Validate cross-storage references
- Check seeding data consistency
- Test application functionality

## Phase 6: Documentation and Deployment

### 6.1 Update Documentation
- Update migration documentation
- Revise seeding architecture docs
- Create deployment guide
- Document rollback procedures

### 6.2 Deployment Strategy
- Staging environment testing
- Production backup procedures
- Migration execution plan
- Monitoring and validation steps

## Risk Mitigation

### High-Risk Areas
1. **Data Loss**: Comprehensive backup strategy
2. **Foreign Key Violations**: Thorough testing with seed data
3. **Index Conflicts**: Clean index management
4. **Cross-Storage Consistency**: Validation scripts

### Rollback Plan
1. Database restore from backup
2. Revert to original migration files
3. Re-run original migrations
4. Validate data integrity

## Success Criteria

### Technical Metrics
- All migrations apply cleanly on fresh database
- Existing databases migrate without data loss
- Seeding process completes successfully
- All tests pass
- Performance benchmarks maintained

### Business Metrics
- Application functionality preserved
- Multi-storage architecture operational
- Seeding data supports all documented scenarios
- Development team productivity maintained

## Timeline

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| Phase 1: Backup & Prep | 2 days | None |
| Phase 2: Create Migrations | 3 days | Phase 1 complete |
| Phase 3: Generate Commands | 1 day | Phase 2 complete |
| Phase 4: Seeding Integration | 2 days | Phase 3 complete |
| Phase 5: Testing | 3 days | Phase 4 complete |
| Phase 6: Documentation | 2 days | Phase 5 complete |

**Total Duration**: 2 weeks (13 days)

## Next Steps

1. **Immediate**: Review and approve consolidation plan
2. **Day 1**: Begin Phase 1 backup and preparation
3. **Day 3**: Start creating consolidated migration structure
4. **Week 2**: Testing and validation
5. **Week 3**: Documentation and deployment preparation