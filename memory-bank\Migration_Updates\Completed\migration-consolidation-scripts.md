# Migration Consolidation Scripts and Templates

## Pre-Consolidation Backup Script

### PowerShell Backup Script
```powershell
# backup-migrations.ps1
$BackupDate = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupDir = "FY.WB.CSHero2.Infrastructure\Migrations\Backup\$BackupDate"
$MigrationsDir = "FY.WB.CSHero2.Infrastructure\Migrations"

Write-Host "Creating backup directory: $BackupDir"
New-Item -ItemType Directory -Path $BackupDir -Force

Write-Host "Backing up existing migrations..."
Copy-Item "$MigrationsDir\*.cs" -Destination $BackupDir
Copy-Item "$MigrationsDir\*.Designer.cs" -Destination $BackupDir

Write-Host "Generating current schema script..."
dotnet ef migrations script --output "$BackupDir\current-schema.sql" --project FY.WB.CSHero2.Infrastructure

Write-Host "Backup completed successfully to: $BackupDir"
```

## Consolidated Migration Templates

### 01_Foundation Migration Template
```csharp
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Foundation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // ASP.NET Identity Tables
            CreateIdentityTables(migrationBuilder);
            
            // Core Business Entities
            CreateTenantProfilesTable(migrationBuilder);
            CreateClientsTable(migrationBuilder);
            CreateTemplatesTable(migrationBuilder);
            
            // Indexes and Constraints
            CreateFoundationIndexes(migrationBuilder);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop in reverse dependency order
            migrationBuilder.DropTable("Clients");
            migrationBuilder.DropTable("Templates");
            migrationBuilder.DropTable("AspNetUserTokens");
            migrationBuilder.DropTable("AspNetUserRoles");
            migrationBuilder.DropTable("AspNetUserLogins");
            migrationBuilder.DropTable("AspNetUserClaims");
            migrationBuilder.DropTable("AspNetRoleClaims");
            migrationBuilder.DropTable("AspNetUsers");
            migrationBuilder.DropTable("AspNetRoles");
            migrationBuilder.DropTable("TenantProfiles");
        }

        private void CreateIdentityTables(MigrationBuilder migrationBuilder)
        {
            // ASP.NET Identity implementation
            // [Implementation details from original Initial migration]
        }

        private void CreateTenantProfilesTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TenantProfiles",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Company = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Subscription = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LastLoginTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    BillingCycle = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    NextBillingDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SubscriptionStatus = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    PaymentMethod = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    BillingAddress = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TenantProfiles", x => x.Id);
                });
        }

        private void CreateClientsTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Clients",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CompanyName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Phone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Address = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    CompanySize = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Industry = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Clients", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Clients_TenantProfiles_TenantId",
                        column: x => x.TenantId,
                        principalTable: "TenantProfiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });
        }

        private void CreateTemplatesTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Templates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ThumbnailUrl = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Tags = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "[]"),
                    Sections = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "[]"),
                    Fields = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "[]"),
                    // Enhanced properties for multi-storage
                    DefaultStyleJson = table.Column<string>(type: "nvarchar(max)", nullable: false, defaultValue: "{}"),
                    EstimatedCompletionTimeMinutes = table.Column<int>(type: "int", nullable: false, defaultValue: 30),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    IsPublic = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    UsageCount = table.Column<int>(type: "int", nullable: false, defaultValue: 0),
                    Version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "1.0.0"),
                    StyleDocumentId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    // Audit fields
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Templates", x => x.Id);
                });
        }

        private void CreateFoundationIndexes(MigrationBuilder migrationBuilder)
        {
            // TenantProfiles indexes
            migrationBuilder.CreateIndex(
                name: "IX_TenantProfiles_Email",
                table: "TenantProfiles",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TenantProfiles_Status_Subscription",
                table: "TenantProfiles",
                columns: new[] { "Status", "Subscription" });

            // Clients indexes
            migrationBuilder.CreateIndex(
                name: "IX_Clients_TenantId",
                table: "Clients",
                column: "TenantId");

            migrationBuilder.CreateIndex(
                name: "IX_Clients_TenantId_Email",
                table: "Clients",
                columns: new[] { "TenantId", "Email" },
                unique: true);

            // Templates indexes
            migrationBuilder.CreateIndex(
                name: "IX_Templates_Category",
                table: "Templates",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_Templates_IsPublic_IsActive_Category",
                table: "Templates",
                columns: new[] { "IsPublic", "IsActive", "Category" });
        }
    }
}
```

### 02_ReportStructure Migration Template
```csharp
using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FY.WB.CSHero2.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ReportStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            CreateReportsTable(migrationBuilder);
            CreateReportVersionsTable(migrationBuilder);
            CreateReportSectionsTable(migrationBuilder);
            CreateReportSectionFieldsTable(migrationBuilder);
            CreateReportStructureIndexes(migrationBuilder);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable("ReportSectionFields");
            migrationBuilder.DropTable("ReportSections");
            migrationBuilder.DropTable("ReportVersions");
            migrationBuilder.DropTable("Reports");
        }

        private void CreateReportsTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Reports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ClientId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ClientName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SlideCount = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Author = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    // Enhanced properties for multi-storage
                    ReportType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "Standard"),
                    TemplateId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    CurrentVersionId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DataDocumentId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ComponentsBlobId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    // Audit fields
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    TenantId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    DeleterId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeletionTime = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Reports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Reports_Clients_ClientId",
                        column: x => x.ClientId,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Reports_Templates_TemplateId",
                        column: x => x.TemplateId,
                        principalTable: "Templates",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });
        }

        private void CreateReportVersionsTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReportVersions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    VersionNumber = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ComponentDataJson = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    JsonData = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsCurrent = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    ComponentDataSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    JsonDataSize = table.Column<long>(type: "bigint", nullable: false, defaultValue: 0L),
                    // Multi-storage properties
                    DataDocumentId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ComponentsBlobId = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    DataBlobPath = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsDataInBlob = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    StyleDocumentId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    // Audit fields
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportVersions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportVersions_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }

        private void CreateReportSectionsTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReportSections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ReportId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSections_Reports_ReportId",
                        column: x => x.ReportId,
                        principalTable: "Reports",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }

        private void CreateReportSectionFieldsTable(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ReportSectionFields",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SectionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSectionFields", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSectionFields_ReportSections_SectionId",
                        column: x => x.SectionId,
                        principalTable: "ReportSections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }

        private void CreateReportStructureIndexes(MigrationBuilder migrationBuilder)
        {
            // Reports indexes
            migrationBuilder.CreateIndex(
                name: "IX_Reports_ClientId",
                table: "Reports",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_Reports_ReportNumber",
                table: "Reports",
                column: "ReportNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Reports_TemplateId_ReportType",
                table: "Reports",
                columns: new[] { "TemplateId", "ReportType" });

            // ReportVersions indexes
            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_ReportId_VersionNumber",
                table: "ReportVersions",
                columns: new[] { "ReportId", "VersionNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_IsCurrent",
                table: "ReportVersions",
                column: "IsCurrent");

            migrationBuilder.CreateIndex(
                name: "IX_ReportVersions_CreationTime",
                table: "ReportVersions",
                column: "CreationTime");

            // ReportSections indexes
            migrationBuilder.CreateIndex(
                name: "IX_ReportSections_ReportId_Order",
                table: "ReportSections",
                columns: new[] { "ReportId", "Order" });

            // ReportSectionFields indexes
            migrationBuilder.CreateIndex(
                name: "IX_ReportSectionFields_SectionId_Order",
                table: "ReportSectionFields",
                columns: new[] { "SectionId", "Order" });
        }
    }
}
```

## Seeding Integration Script

### Updated DataSeeder Structure
```csharp
// Enhanced DataSeeder.cs for consolidated migrations
public class DataSeeder
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DataSeeder> _logger;

    public async Task SeedAsync()
    {
        try
        {
            // Phase 1: Foundation entities (01_Foundation)
            await SeedFoundationEntitiesAsync();
            
            // Phase 2: Report structure (02_ReportStructure)
            await SeedReportStructureAsync();
            
            // Phase 3: Multi-storage integration (03_MultiStorageIntegration)
            await SeedMultiStorageIntegrationAsync();
            
            // Phase 4: Supporting entities (04_SupportingEntities)
            await SeedSupportingEntitiesAsync();
            
            await _context.SaveChangesAsync();
            _logger.LogInformation("Data seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during data seeding");
            throw;
        }
    }

    private async Task SeedFoundationEntitiesAsync()
    {
        // 1. TenantProfiles (independent)
        await SeedTenantProfilesAsync();
        
        // 2. ApplicationUsers (depends on TenantProfiles)
        await SeedApplicationUsersAsync();
        
        // 3. Clients (depends on TenantProfiles)
        await SeedClientsAsync();
        
        // 4. Templates (independent, enhanced for multi-storage)
        await SeedTemplatesAsync();
    }

    private async Task SeedReportStructureAsync()
    {
        // 5. Reports (depends on Clients, Templates)
        await SeedReportsAsync();
        
        // 6. ReportVersions (depends on Reports)
        await SeedReportVersionsAsync();
        
        // 7. ReportSections (depends on Reports)
        await SeedReportSectionsAsync();
        
        // 8. ReportSectionFields (depends on ReportSections)
        await SeedReportSectionFieldsAsync();
    }

    private async Task SeedMultiStorageIntegrationAsync()
    {
        // 9. ReportStyles (depends on Reports)
        await SeedReportStylesAsync();
        
        // 10. ComponentDefinitions (depends on ReportVersions)
        await SeedComponentDefinitionsAsync();
        
        // 11. Cross-storage references
        await UpdateCrossStorageReferencesAsync();
    }

    private async Task SeedSupportingEntitiesAsync()
    {
        // 12. Forms (depends on TenantProfiles)
        await SeedFormsAsync();
        
        // 13. Invoices (depends on TenantProfiles)
        await SeedInvoicesAsync();
        
        // 14. Uploads (independent)
        await SeedUploadsAsync();
    }
}
```

## Validation Scripts

### Database Integrity Check
```sql
-- validate-migration-integrity.sql
-- Check for orphaned records after consolidation

-- 1. Verify all foreign key relationships
SELECT 'Clients without TenantProfiles' as Issue, COUNT(*) as Count
FROM Clients c
LEFT JOIN TenantProfiles tp ON c.TenantId = tp.Id
WHERE tp.Id IS NULL;

SELECT 'Reports without Clients' as Issue, COUNT(*) as Count
FROM Reports r
LEFT JOIN Clients c ON r.ClientId = c.Id
WHERE c.Id IS NULL;

SELECT 'ReportSections without Reports' as Issue, COUNT(*) as Count
FROM ReportSections rs
LEFT JOIN Reports r ON rs.ReportId = r.Id
WHERE r.Id IS NULL;

SELECT 'ReportSectionFields without ReportSections' as Issue, COUNT(*) as Count
FROM ReportSectionFields rsf
LEFT JOIN ReportSections rs ON rsf.SectionId = rs.Id
WHERE rs.Id IS NULL;

-- 2. Verify seeding data consistency
SELECT 'Total TenantProfiles' as Entity, COUNT(*) as Count FROM TenantProfiles
UNION ALL
SELECT 'Total Clients', COUNT(*) FROM Clients
UNION ALL
SELECT 'Total Reports', COUNT(*) FROM Reports
UNION ALL
SELECT 'Total ReportSections', COUNT(*) FROM ReportSections
UNION ALL
SELECT 'Total ReportSectionFields', COUNT(*) FROM ReportSectionFields;

-- 3. Check multi-storage references
SELECT 'Reports with DataDocumentId' as Check, COUNT(*) as Count
FROM Reports
WHERE DataDocumentId IS NOT NULL;

SELECT 'ReportVersions with ComponentsBlobId' as Check, COUNT(*) as Count
FROM ReportVersions
WHERE ComponentsBlobId IS NOT NULL;
```

## Deployment Checklist

### Pre-Deployment
- [ ] Backup current database
- [ ] Backup existing migration files
- [ ] Test consolidation on development environment
- [ ] Validate seeding data integrity
- [ ] Performance benchmark current system

### Deployment
- [ ] Remove existing migration files
- [ ] Apply consolidated migrations
- [ ] Run seeding process
- [ ] Validate database integrity
- [ ] Run application tests

### Post-Deployment
- [ ] Monitor application performance
- [ ] Validate multi-storage functionality
- [ ] Update documentation
- [ ] Train development team on new structure