# Migration Consolidation Execution Guide

## Executive Summary

This guide provides step-by-step instructions for safely consolidating the Entity Framework migrations in the FY.WB.CSHero2 project. The consolidation will transform 4 fragmented migrations into 4 logical, well-organized migrations that align with the documented seeding architecture and multi-storage design.

## Pre-Execution Safety Checklist

### 1. Environment Preparation
```bash
# Verify current environment
dotnet --version
dotnet ef --version

# Ensure you're in the correct directory
cd C:\Users\<USER>\Documents\GitHub\ChildrensVillage\FY.WB.CSHero2

# Check current migration status
dotnet ef migrations list --project FY.WB.CSHero2.Infrastructure
```

### 2. Create Complete Backup
```powershell
# Run the backup script
.\backup-migrations.ps1

# Verify backup was created
ls FY.WB.CSHero2.Infrastructure\Migrations\Backup\

# Export current database schema
dotnet ef migrations script --output "backup-current-schema.sql" --project FY.WB.CSHero2.Infrastructure
```

### 3. Test Environment Validation
```bash
# Test current application functionality
dotnet build
dotnet test

# Verify seeding works with current migrations
dotnet run --project FY.WB.CSHero2 -- seed-data --environment Development
```

## Step-by-Step Execution

### Step 1: Remove Existing Migrations (CRITICAL - NO UNDO)

⚠️ **WARNING**: This step cannot be undone. Ensure backups are complete before proceeding.

```bash
# Navigate to migrations directory
cd FY.WB.CSHero2.Infrastructure\Migrations

# Remove existing migration files (keep backup!)
Remove-Item "20250522172751_Initial.*"
Remove-Item "20250526220454_AddReportRenderingV2EntitiesPhase1.*"
Remove-Item "20250602210411_AddMultiStorageSupport.*"
Remove-Item "20250602224758_AddReportSectionEntities.*"

# Keep the ApplicationDbContextModelSnapshot.cs - it will be regenerated
```

### Step 2: Generate Consolidated Migrations

#### 2.1 Foundation Migration
```bash
# Generate foundation migration
dotnet ef migrations add 01_Foundation --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Verify migration was created
ls FY.WB.CSHero2.Infrastructure\Migrations\*Foundation*
```

#### 2.2 Report Structure Migration
```bash
# Generate report structure migration
dotnet ef migrations add 02_ReportStructure --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Verify migration was created
ls FY.WB.CSHero2.Infrastructure\Migrations\*ReportStructure*
```

#### 2.3 Multi-Storage Integration Migration
```bash
# Generate multi-storage integration migration
dotnet ef migrations add 03_MultiStorageIntegration --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Verify migration was created
ls FY.WB.CSHero2.Infrastructure\Migrations\*MultiStorageIntegration*
```

#### 2.4 Supporting Entities Migration
```bash
# Generate supporting entities migration
dotnet ef migrations add 04_SupportingEntities --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Verify migration was created
ls FY.WB.CSHero2.Infrastructure\Migrations\*SupportingEntities*
```

### Step 3: Customize Generated Migrations

The auto-generated migrations will need manual customization to match our consolidation plan. For each migration file:

#### 3.1 Update Migration Names and Comments
```csharp
// Example for 01_Foundation migration
/// <summary>
/// Foundation migration: Core application infrastructure including Identity, TenantProfiles, Clients, and Templates.
/// Consolidates: Initial migration + Template enhancements
/// Seeding: TenantProfiles, ApplicationUsers, Clients, Templates
/// </summary>
public partial class Foundation : Migration
```

#### 3.2 Organize Methods by Logical Groups
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Group 1: Identity Infrastructure
    CreateIdentityTables(migrationBuilder);
    
    // Group 2: Core Business Entities
    CreateTenantProfilesTable(migrationBuilder);
    CreateClientsTable(migrationBuilder);
    CreateTemplatesTable(migrationBuilder);
    
    // Group 3: Indexes and Constraints
    CreateFoundationIndexes(migrationBuilder);
}
```

#### 3.3 Add Comprehensive Comments
```csharp
private void CreateTenantProfilesTable(MigrationBuilder migrationBuilder)
{
    // TenantProfiles: Root entity for multi-tenancy
    // Dependencies: None (independent entity)
    // Seeding: 4 tenant configurations from tenant-profiles.json
    migrationBuilder.CreateTable(
        name: "TenantProfiles",
        // ... table definition
    );
}
```

### Step 4: Test Fresh Database Creation

```bash
# Test on a fresh database
dotnet ef database drop --force --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Apply all consolidated migrations
dotnet ef database update --project FY.WB.CSHero2.Infrastructure --startup-project FY.WB.CSHero2

# Verify database structure
dotnet ef migrations script --output "consolidated-schema.sql" --project FY.WB.CSHero2.Infrastructure
```

### Step 5: Test Seeding Integration

```bash
# Run the updated seeding process
dotnet run --project FY.WB.CSHero2 -- seed-data --environment Development

# Verify seeded data
# Connect to database and run validation queries from migration-consolidation-scripts.md
```

### Step 6: Validate Application Functionality

```bash
# Build and test the application
dotnet build
dotnet test

# Run the application
dotnet run --project FY.WB.CSHero2

# Test key functionality:
# - User authentication
# - Tenant isolation
# - Report creation
# - Multi-storage operations
```

## Rollback Procedures

### If Issues Occur During Step 1-2 (Migration Generation)
```bash
# Restore original migration files from backup
$BackupDir = "FY.WB.CSHero2.Infrastructure\Migrations\Backup\[TIMESTAMP]"
Copy-Item "$BackupDir\*.cs" -Destination "FY.WB.CSHero2.Infrastructure\Migrations\"

# Reset database to original state
dotnet ef database drop --force --project FY.WB.CSHero2.Infrastructure
dotnet ef database update --project FY.WB.CSHero2.Infrastructure
```

### If Issues Occur During Step 3-6 (Testing)
```bash
# Option 1: Fix migration issues and retry
# Edit the problematic migration files
# Re-test with fresh database

# Option 2: Complete rollback
# Restore original migrations (as above)
# Restore original database from backup
```

### Database Restore from Backup
```sql
-- If you have a database backup
USE master;
ALTER DATABASE [YourDatabaseName] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
RESTORE DATABASE [YourDatabaseName] FROM DISK = 'path\to\backup.bak' WITH REPLACE;
ALTER DATABASE [YourDatabaseName] SET MULTI_USER;
```

## Post-Consolidation Tasks

### 1. Update Documentation
- [ ] Update migration documentation in memory-bank
- [ ] Revise seeding architecture documentation
- [ ] Update development team procedures
- [ ] Document new migration naming conventions

### 2. Team Communication
- [ ] Notify team of new migration structure
- [ ] Provide training on consolidated approach
- [ ] Update CI/CD pipelines if necessary
- [ ] Update deployment procedures

### 3. Monitoring and Validation
- [ ] Monitor application performance post-consolidation
- [ ] Validate multi-storage functionality
- [ ] Check seeding performance metrics
- [ ] Verify tenant isolation still works correctly

## Success Criteria Validation

### Technical Validation
```bash
# 1. All migrations apply cleanly
dotnet ef migrations list --project FY.WB.CSHero2.Infrastructure

# 2. Database schema matches expectations
# Compare consolidated-schema.sql with backup-current-schema.sql

# 3. Seeding completes successfully
dotnet run --project FY.WB.CSHero2 -- seed-data --validate

# 4. Application tests pass
dotnet test --verbosity normal

# 5. Performance benchmarks maintained
# Run performance tests and compare with baseline
```

### Business Validation
- [ ] User authentication works correctly
- [ ] Tenant isolation is maintained
- [ ] Report creation and editing functions
- [ ] Multi-storage operations work as expected
- [ ] All seeded data is accessible and correct

## Troubleshooting Common Issues

### Issue: Migration Generation Fails
**Symptoms**: `dotnet ef migrations add` command fails
**Solutions**:
1. Ensure DbContext is properly configured
2. Check for compilation errors in domain entities
3. Verify connection string is accessible
4. Check for conflicting entity configurations

### Issue: Foreign Key Constraint Violations
**Symptoms**: Migration fails during database update
**Solutions**:
1. Review entity relationships in migration
2. Ensure proper dependency order in Up() method
3. Check for missing parent entities
4. Validate foreign key column types match

### Issue: Seeding Fails After Consolidation
**Symptoms**: Seeding process throws exceptions
**Solutions**:
1. Verify seeding order matches migration dependencies
2. Check JSON seed data for consistency
3. Ensure all required entities exist before dependent entities
4. Validate foreign key references in seed data

### Issue: Index Creation Conflicts
**Symptoms**: Duplicate index errors during migration
**Solutions**:
1. Check for duplicate index definitions
2. Use conditional index creation SQL
3. Ensure index names are unique across migrations
4. Review index dependencies

## Emergency Contacts and Resources

### Key Documentation
- [Seeding Architecture Guide](memory-bank/DataSeeder_Documentation/seeding_architecture.md)
- [Multi-Storage Design](memory-bank/report_refactoring/README.md)
- [Entity Relationships](memory-bank/report_refactoring/documentation/entity_relationships.md)

### Backup Locations
- Migration files: `FY.WB.CSHero2.Infrastructure\Migrations\Backup\[TIMESTAMP]`
- Database schema: `backup-current-schema.sql`
- Seeding data: `FY.WB.CSHero2.Infrastructure\Persistence\SeedData\`

### Recovery Commands
```bash
# Quick rollback to original state
git checkout HEAD -- FY.WB.CSHero2.Infrastructure/Migrations/
dotnet ef database drop --force --project FY.WB.CSHero2.Infrastructure
dotnet ef database update --project FY.WB.CSHero2.Infrastructure
```

---

**⚠️ IMPORTANT REMINDER**: Always test the consolidation process in a development environment before applying to staging or production. Ensure all backups are complete and verified before beginning the consolidation process.