# Cosmos DB Partition Key Mismatch Analysis & Solution

## Problem Analysis

### Root Cause
The Cosmos DB container "Reports" is configured with partition key path `/partitionKey`, but the seeding code is creating documents that don't have a `partitionKey` field. This causes the error:

```
"PartitionKey extracted from document doesn't match the one specified in the header"
```

### Current Implementation Issues

1. **Container Configuration**: `/partitionKey` 
2. **Document Structure**: Only has `tenantId` field
3. **Code Mismatch**: Passes `document.TenantId` as partition key but document lacks `partitionKey` property

### Architecture Design vs Implementation

According to the design documents:

**From multi_storage_design.md (line 420)**:
```csharp
var response = await _container.ReadItemAsync<ReportData>(
    documentId, 
    new PartitionKey(documentId), // Uses documentId as partition key
    cancellationToken: cancellationToken);
```

**From architecture_diagrams.md (line 663)**:
```json
{
  "id": "report-data-guid",
  "reportId": "guid",
  "versionId": "guid",
  // No explicit partitionKey field shown
}
```

## Analysis of Current vs Intended Design

### Current Seeding Implementation
- Creates `VersionedReportDataDocument` with `tenantId` field
- Passes `document.TenantId` as partition key parameter
- Container expects `/partitionKey` field in document

### Intended Design (from architecture docs)
- Uses document `id` as partition key value
- Simpler partitioning strategy
- Better distribution for report data

## Solution Options

### Option 1: Fix Document Structure (Quick Fix)
Add `partitionKey` field to documents and set it to `tenantId`:

```csharp
public class VersionedReportDataDocument
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("tenantId")]
    public string TenantId { get; set; } = string.Empty;

    [JsonPropertyName("partitionKey")]
    public string PartitionKey { get; set; } = string.Empty; // ADD THIS

    // ... rest of properties
}
```

**Pros**: Minimal code changes, works with current container
**Cons**: Deviates from intended architecture design

### Option 2: Align with Architecture Design (Recommended)
Reconfigure container to use `/id` as partition key and update code:

```csharp
// Update UpsertItemAsync calls to use document ID
await _cosmosDbService.UpsertItemAsync(document, document.Id);
```

**Pros**: Aligns with architecture design, better data distribution
**Cons**: Requires container recreation and data migration

### Option 3: Use TenantId as Partition Key (Alternative)
Reconfigure container to use `/tenantId` as partition key:

**Pros**: Logical tenant-based partitioning, aligns with multi-tenancy
**Cons**: May cause hot partitions if tenant data is uneven

## Recommended Solution: Option 2 (Architecture Alignment)

### Implementation Plan

#### Step 1: Container Reconfiguration
1. Create new container with partition key `/id`
2. Migrate existing data (if any)
3. Update configuration

#### Step 2: Code Updates
Update seeding code to use document ID as partition key:

```csharp
// In CosmosSeeder.cs
await _cosmosDbService.UpsertItemAsync(document, document.Id);

// In CosmosDbService.cs - no changes needed, already correct
public async Task<T> UpsertItemAsync<T>(T item, string partitionKey) where T : class
{
    var response = await _container.UpsertItemAsync(item, new PartitionKey(partitionKey));
    return response.Resource;
}
```

#### Step 3: Update All Repository Implementations
Ensure all Cosmos DB operations use document ID as partition key:

```csharp
// ReportDataRepository.cs
var response = await _container.ReadItemAsync<ReportData>(
    documentId, 
    new PartitionKey(documentId), // Use documentId as partition key
    cancellationToken: cancellationToken);
```

### Migration Script

```sql
-- If needed, backup existing data first
-- Then recreate container with correct partition key
```

### Configuration Update

```json
{
  "CosmosDb": {
    "ConnectionString": "...",
    "DatabaseName": "CSHeroReports",
    "ContainerName": "Reports",
    "PartitionKeyPath": "/id"  // Update this
  }
}
```

## Benefits of Recommended Solution

1. **Alignment**: Matches intended architecture design
2. **Performance**: Better data distribution using document ID
3. **Consistency**: All operations use same partitioning strategy
4. **Scalability**: Avoids potential hot partitions from tenant-based partitioning

## Implementation Steps

1. **Immediate Fix** (Option 1): Add `partitionKey` field to resolve current error
2. **Long-term Solution** (Option 2): Implement architecture-aligned approach
3. **Testing**: Verify seeding works with both approaches
4. **Documentation**: Update implementation docs to match architecture

## Code Changes Required

### Immediate Fix (Option 1)
```csharp
// In CreateVersionedReportDocumentAsync
return new VersionedReportDataDocument
{
    Id = GenerateVersionedDocumentId(report.Id, version.Id),
    TenantId = tenantId,
    PartitionKey = tenantId, // ADD THIS LINE
    // ... rest of properties
};

// Update UpsertItemAsync call
await _cosmosDbService.UpsertItemAsync(document, document.PartitionKey);
```

### Long-term Solution (Option 2)
```csharp
// No document structure changes needed
// Just update UpsertItemAsync call
await _cosmosDbService.UpsertItemAsync(document, document.Id);
```

## Conclusion

The partition key mismatch error is caused by inconsistency between container configuration (`/partitionKey`) and document structure (missing `partitionKey` field). 

**Immediate recommendation**: Implement Option 1 to resolve the seeding error quickly.

**Strategic recommendation**: Plan for Option 2 to align with the intended architecture design for better long-term maintainability and performance.