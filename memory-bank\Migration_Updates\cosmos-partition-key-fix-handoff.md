# Cosmos DB Partition Key Mismatch Fix - Implementation Handoff

## Task Overview

**Issue**: Cosmos DB seeding is failing with partition key mismatch error during data seeding process.

**Error Message**: 
```
"PartitionKey extracted from document doesn't match the one specified in the header. Learn more: https://aka.ms/CosmosDB/sql/errors/wrong-pk-value"
```

**Root Cause**: Missing tenant data and document structure mismatch with container partition key configuration.

## Analysis Summary

### Primary Issues Identified

1. **Missing Tenant Data**: `TenantProfiles` table may not contain active tenants required for partition key generation
2. **Document Structure Mismatch**: Container configured with `/partitionKey` path but documents only have `tenantId` field
3. **Invalid Fallback Values**: Reports without valid TenantId fall back to "default" partition key value

### Files Created During Analysis

- `memory-bank/Migration_Updates/cosmos-partition-key-fix-analysis.md` - Technical analysis and solution options
- `memory-bank/Migration_Updates/cosmos-partition-key-root-cause-analysis.md` - Root cause investigation and action plan

## Implementation Tasks

### Task 1: Data Validation and Fixes (Priority: HIGH)

**Objective**: Ensure tenant data is available for partition key generation

**Steps**:
1. **Validate Tenant Data**:
   ```sql
   SELECT COUNT(*) as ActiveTenants 
   FROM TenantProfiles 
   WHERE Status = 'active' AND TenantId IS NOT NULL;
   ```

2. **Create Missing Tenant Data** (if needed):
   ```sql
   INSERT INTO TenantProfiles (Id, TenantId, Company, Name, Email, Status, Subscription)
   VALUES 
       (NEWID(), NEWID(), 'TechCorp Solutions', 'John Doe', '<EMAIL>', 'active', 'premium'),
       (NEWID(), NEWID(), 'Health Plus', 'Jane Smith', '<EMAIL>', 'active', 'standard'),
       (NEWID(), NEWID(), 'TechFusion Inc.', 'Bob Johnson', '<EMAIL>', 'active', 'premium');
   ```

3. **Validate Report TenantId References**:
   ```sql
   SELECT COUNT(*) as ReportsWithoutTenantId 
   FROM Reports 
   WHERE TenantId IS NULL OR TenantId = '00000000-0000-0000-0000-000000000000';
   ```

4. **Fix Report TenantId References** (if needed):
   ```sql
   DECLARE @FirstTenantId UNIQUEIDENTIFIER = (
       SELECT TOP 1 TenantId FROM TenantProfiles 
       WHERE Status = 'active' AND TenantId IS NOT NULL
   );
   
   UPDATE Reports 
   SET TenantId = @FirstTenantId 
   WHERE TenantId IS NULL OR TenantId = '00000000-0000-0000-0000-000000000000';
   ```

**Acceptance Criteria**:
- At least 3 active tenants exist in TenantProfiles table
- All reports have valid TenantId references
- TenantResolutionService.GetSeededTenantsAsync() returns non-empty list

### Task 2: Document Structure Fix (Priority: HIGH)

**Objective**: Add missing `partitionKey` field to Cosmos DB documents

**Files to Modify**:
- `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Changes Required**:

1. **Update VersionedReportDataDocument class**:
   ```csharp
   public class VersionedReportDataDocument
   {
       [JsonPropertyName("id")]
       public string Id { get; set; } = string.Empty;

       [JsonPropertyName("tenantId")]
       public string TenantId { get; set; } = string.Empty;

       [JsonPropertyName("partitionKey")]
       public string PartitionKey { get; set; } = string.Empty; // ADD THIS

       // ... rest of existing properties
   }
   ```

2. **Update CreateVersionedReportDocumentAsync method**:
   ```csharp
   return new VersionedReportDataDocument
   {
       Id = GenerateVersionedDocumentId(report.Id, version.Id),
       TenantId = tenantId,
       PartitionKey = tenantId, // ADD THIS LINE
       // ... rest of existing properties
   };
   ```

3. **Update UpsertItemAsync call**:
   ```csharp
   // Change from:
   await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
   
   // To:
   await _cosmosDbService.UpsertItemAsync(document, document.PartitionKey);
   ```

**Acceptance Criteria**:
- Documents contain both `tenantId` and `partitionKey` fields
- `partitionKey` value matches the value passed to UpsertItemAsync
- No partition key mismatch errors during seeding

### Task 3: Enhanced Logging and Validation (Priority: MEDIUM)

**Objective**: Add diagnostic logging to prevent future issues

**Changes Required**:

1. **Add tenant validation in CosmosSeeder.SeedAsync()**:
   ```csharp
   // Add after line 46 in CosmosSeeder.cs
   var tenants = await tenantResolutionService.GetSeededTenantsAsync();
   if (!tenants.Any())
   {
       _logger.LogError("No active tenants found in TenantProfiles table - cannot proceed with Cosmos seeding");
       _logger.LogError("Please ensure TenantProfiles table contains active tenants with valid TenantId values");
       return;
   }
   _logger.LogInformation("Found {TenantCount} active tenants for Cosmos seeding", tenants.Count);
   ```

2. **Add partition key logging**:
   ```csharp
   // Add before UpsertItemAsync call
   _logger.LogDebug("Upserting document {DocumentId} with partition key '{PartitionKey}' for report {ReportId} version {VersionId}",
       document.Id, document.PartitionKey, report.Id, version.Id);
   ```

**Acceptance Criteria**:
- Clear error messages when tenant data is missing
- Detailed logging of partition key values during seeding
- Early exit when prerequisites are not met

### Task 4: Testing and Validation (Priority: MEDIUM)

**Objective**: Verify the fix works correctly

**Test Cases**:

1. **Test with Missing Tenant Data**:
   - Clear TenantProfiles table
   - Run seeding
   - Verify graceful failure with clear error message

2. **Test with Valid Tenant Data**:
   - Ensure active tenants exist
   - Run seeding
   - Verify successful completion without partition key errors

3. **Test Partition Key Values**:
   - Verify documents in Cosmos DB have correct `partitionKey` field
   - Verify partition key values match TenantId values

**Acceptance Criteria**:
- All test cases pass
- Seeding completes successfully with valid tenant data
- No partition key mismatch errors in logs

## Alternative Solutions (Future Consideration)

### Option A: Align with Architecture Design
- Reconfigure Cosmos DB container to use `/id` as partition key
- Update all code to use document ID as partition key
- Better aligns with intended architecture design

### Option B: Use TenantId as Partition Key
- Reconfigure container to use `/tenantId` as partition key
- Remove need for separate `partitionKey` field
- More intuitive for multi-tenant architecture

## Dependencies

- Access to SQL database for tenant data validation/creation
- Access to Cosmos DB for testing
- Ability to modify seeding code

## Estimated Effort

- **Task 1 (Data Fixes)**: 2-4 hours
- **Task 2 (Code Changes)**: 4-6 hours  
- **Task 3 (Logging)**: 2-3 hours
- **Task 4 (Testing)**: 3-4 hours

**Total**: 11-17 hours

## Success Criteria

1. Cosmos DB seeding completes without partition key mismatch errors
2. All reports are successfully seeded to Cosmos DB
3. Documents contain proper partition key fields
4. Clear error handling for missing tenant data
5. Comprehensive logging for troubleshooting

## Rollback Plan

If issues occur:
1. Revert code changes to original state
2. Restore database if data changes were made
3. Document any new issues discovered
4. Consider alternative solution approaches

## Next Steps After Completion

1. Monitor seeding process in production
2. Consider implementing architecture-aligned solution (Option A)
3. Add automated tests for tenant data validation
4. Document partition key strategy for future development