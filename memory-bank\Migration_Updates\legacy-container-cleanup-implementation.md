# Legacy Container Creation Cleanup - Implementation Guide

## 📋 **Overview**

This document provides step-by-step implementation instructions for removing legacy Cosmos DB container creation logic and aligning all components with the current architecture.

**Prerequisites**: Review `legacy-container-cleanup-handoff.md` before proceeding.

## 🔧 **Phase 1: Remove Legacy Migration Service**

### **Step 1.1: Delete ReportDataMigrationService File**

**File to Delete**:
```
FY.WB.CSHero2.Infrastructure/Persistence/Migrations/ReportDataMigrationService.cs
```

**Action**: Delete the entire file.

### **Step 1.2: Find and Remove Interface References**

**Search for**: `IReportDataMigrationService`

**Expected Locations**:
- Application layer interfaces
- Dependency injection configurations
- Controller constructors

**Action**: Remove all references and imports.

### **Step 1.3: Remove from Dependency Injection**

**File**: `FY.WB.CSHero2.Infrastructure/DependencyInjection.cs`

**Search for**:
```csharp
services.AddScoped<IReportDataMigrationService, ReportDataMigrationService>();
```

**Action**: Remove the line if found.

### **Step 1.4: Remove Controller Endpoints**

**File**: `FY.WB.CSHero2/Controllers/MigrationController.cs`

**Search for**:
- Methods using `IReportDataMigrationService`
- Migration-related endpoints
- Constructor parameters with migration service

**Action**: Remove migration service usage, keep other functionality.

## 🔧 **Phase 2: Clean Program.cs Container Creation**

### **Step 2.1: Remove EnsureCosmosDbCreatedAsync Method**

**File**: `FY.WB.CSHero2/Program.cs`

**Remove this entire method** (lines ~180-195):
```csharp
// Helper method to ensure CosmosDB containers are created
static async Task EnsureCosmosDbCreatedAsync(IServiceProvider serviceProvider)
{
    var cosmosClient = serviceProvider.GetRequiredService<Microsoft.Azure.Cosmos.CosmosClient>();
    var options = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration.CosmosDbOptions>>().Value;

    // Create database if it doesn't exist
    var databaseResponse = await cosmosClient.CreateDatabaseIfNotExistsAsync(
        options.DatabaseName,
        throughput: 400); // Shared throughput for cost optimization

    var database = databaseResponse.Database;

    // Create containers if they don't exist
    await database.CreateContainerIfNotExistsAsync(
        options.Containers.ReportStyles,
        "/partitionKey",
        throughput: null); // Use shared database throughput
}
```

### **Step 2.2: Remove Method Call**

**File**: `FY.WB.CSHero2/Program.cs`

**Find and remove** (line ~130):
```csharp
// Ensure CosmosDB containers are created first
logger.LogInformation("Ensuring CosmosDB containers are created...");
await EnsureCosmosDbCreatedAsync(services);
logger.LogInformation("CosmosDB containers created successfully");
```

### **Step 2.3: Update SeedCosmosDbAsync Method**

**File**: `FY.WB.CSHero2/Program.cs`

**Current method** (lines ~120-150):
```csharp
static async Task SeedCosmosDbAsync(IServiceProvider services, ILogger logger)
{
    try
    {
        logger.LogInformation("Starting CosmosDB data seeding...");
        
        // Ensure CosmosDB containers are created first
        logger.LogInformation("Ensuring CosmosDB containers are created...");
        await EnsureCosmosDbCreatedAsync(services);
        logger.LogInformation("CosmosDB containers created successfully");

        // TODO: Re-enable CosmosDB seeding once compilation issues are resolved
        logger.LogInformation("CosmosDB seeding temporarily disabled due to compilation issues");
        logger.LogInformation("CosmosDB containers have been created successfully");

    }
    catch (Exception ex)
    {
        logger.LogError(ex, "CosmosDB seeding failed");
        logger.LogWarning("Application will continue but CosmosDB functionality may be limited");
    }

    // Always attempt blob storage container creation and seeding regardless of CosmosDB seeding result
    try
    {
        logger.LogInformation("Ensuring blob storage container is created...");
        await services.EnsureStorageCreatedAsync();
        logger.LogInformation("Blob storage container created successfully");
        
        var blobServiceForSeeding = services.GetService<IReportDataBlobService>();
        if (blobServiceForSeeding != null)
        {
            logger.LogInformation("IReportDataBlobService found, blob storage container is ready for seeding");
            // Note: Blob seeding can be implemented later when compilation issues are resolved
        }
        else
        {
            logger.LogInformation("IReportDataBlobService not available, but blob storage container is created");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Failed to ensure blob storage container creation");
        logger.LogWarning("Application will continue but blob storage functionality may be limited");
    }
}
```

**Replace with**:
```csharp
static async Task SeedCosmosDbAsync(IServiceProvider services, ILogger logger)
{
    try
    {
        logger.LogInformation("Starting CosmosDB data seeding...");
        
        // Note: CosmosDB containers should be created by setup script, not application
        logger.LogInformation("CosmosDB containers expected to exist from infrastructure setup");

        // TODO: Re-enable CosmosDB seeding once compilation issues are resolved
        logger.LogInformation("CosmosDB seeding temporarily disabled due to compilation issues");
        logger.LogInformation("CosmosDB seeding will use existing containers");

    }
    catch (Exception ex)
    {
        logger.LogError(ex, "CosmosDB seeding failed");
        logger.LogWarning("Application will continue but CosmosDB functionality may be limited");
    }

    // Always attempt blob storage container creation and seeding regardless of CosmosDB seeding result
    try
    {
        logger.LogInformation("Ensuring blob storage container is created...");
        await services.EnsureStorageCreatedAsync();
        logger.LogInformation("Blob storage container created successfully");
        
        var blobServiceForSeeding = services.GetService<IReportDataBlobService>();
        if (blobServiceForSeeding != null)
        {
            logger.LogInformation("IReportDataBlobService found, blob storage container is ready for seeding");
            // Note: Blob seeding can be implemented later when compilation issues are resolved
        }
        else
        {
            logger.LogInformation("IReportDataBlobService not available, but blob storage container is created");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Failed to ensure blob storage container creation");
        logger.LogWarning("Application will continue but blob storage functionality may be limited");
    }
}
```

### **Step 2.4: Remove Unused Imports**

**File**: `FY.WB.CSHero2/Program.cs`

**Check for and remove unused imports**:
```csharp
using FY.WB.CSHero2.ReportRenderingEngine.Infrastructure.Configuration;
```

## 🔧 **Phase 3: Clean ReportRenderingEngine Container Creation**

### **Step 3.1: Update EnsureStorageCreatedAsync Method**

**File**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/DependencyInjection.cs`

**Current method** (lines ~90-100):
```csharp
public static async Task EnsureStorageCreatedAsync(this IServiceProvider serviceProvider)
{
    // Ensure CosmosDB database and containers exist
    await EnsureCosmosDbCreatedAsync(serviceProvider);

    // Ensure Blob Storage containers exist
    await EnsureBlobStorageCreatedAsync(serviceProvider);
}
```

**Replace with**:
```csharp
public static async Task EnsureStorageCreatedAsync(this IServiceProvider serviceProvider)
{
    // Note: CosmosDB containers should be created by setup script, not application
    // Only ensure Blob Storage containers exist
    await EnsureBlobStorageCreatedAsync(serviceProvider);
}
```

### **Step 3.2: Remove EnsureCosmosDbCreatedAsync Method**

**File**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/DependencyInjection.cs`

**Remove this entire method** (lines ~100-120):
```csharp
private static async Task EnsureCosmosDbCreatedAsync(IServiceProvider serviceProvider)
{
    var cosmosClient = serviceProvider.GetRequiredService<CosmosClient>();
    var options = serviceProvider.GetRequiredService<Microsoft.Extensions.Options.IOptions<CosmosDbOptions>>().Value;

    // Create database if it doesn't exist
    var databaseResponse = await cosmosClient.CreateDatabaseIfNotExistsAsync(
        options.DatabaseName,
        throughput: 400); // Shared throughput for cost optimization

    var database = databaseResponse.Database;

    // Create containers if they don't exist
    await database.CreateContainerIfNotExistsAsync(
        options.Containers.ReportStyles,
        "/partitionKey",
        throughput: null); // Use shared database throughput

    await database.CreateContainerIfNotExistsAsync(
        options.Containers.ReportData,
        "/partitionKey",
        throughput: null); // Use shared database throughput
}
```

### **Step 3.3: Remove Unused CosmosDbOptions (Optional)**

**File**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Configuration/CosmosDbOptions.cs`

**Analysis**: Check if this configuration is used elsewhere in the ReportRenderingEngine.

**If not used**: Consider removing the entire file and its references.

**If used**: Keep the file but remove container-specific properties.

## 🔧 **Phase 4: Verification and Configuration**

### **Step 4.1: Verify CosmosSeeder Configuration**

**File**: `FY.WB.CSHero2.Infrastructure/Persistence/Seeders/CosmosSeeder.cs`

**Verify these aspects**:

1. **Uses correct configuration section**:
```csharp
// Should use FY.WB.CSHero2.Infrastructure.Configuration.CosmosDbOptions
// NOT ReportRenderingEngine.Infrastructure.Configuration.CosmosDbOptions
```

2. **Uses correct partition key**:
```csharp
// Should use document.TenantId as partition key
await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
```

3. **Container name from configuration**:
```csharp
// Should use configuration value that matches setup script
// Default: "Reports"
```

### **Step 4.2: Verify appsettings.json Configuration**

**File**: `FY.WB.CSHero2/appsettings.json`

**Ensure configuration matches setup script**:
```json
{
  "CosmosDb": {
    "ConnectionString": "...",
    "DatabaseName": "CSHeroReports",
    "ContainerName": "Reports"
  }
}
```

### **Step 4.3: Remove Migration Service References**

**Search entire solution for**:
- `IReportDataMigrationService`
- `ReportDataMigrationService`
- Migration-related endpoints

**Files to check**:
- Controllers
- Dependency injection configurations
- Application layer services
- Using statements

## 🧪 **Compilation and Basic Testing**

### **Step 4.4: Test Compilation**

```bash
cd FY.WB.CSHero2
dotnet build
```

**Expected**: No compilation errors.

### **Step 4.5: Test Application Startup**

```bash
cd FY.WB.CSHero2
dotnet run
```

**Expected**: 
- Application starts successfully
- Seeding process completes
- No container creation errors
- Blob storage containers created

## 📋 **Code Review Checklist**

### **Removed Components**
- [ ] `ReportDataMigrationService.cs` file deleted
- [ ] All `IReportDataMigrationService` references removed
- [ ] Program.cs `EnsureCosmosDbCreatedAsync` method removed
- [ ] Program.cs method call removed
- [ ] ReportRenderingEngine `EnsureCosmosDbCreatedAsync` method removed
- [ ] Unused imports removed

### **Updated Components**
- [ ] Program.cs `SeedCosmosDbAsync` method updated
- [ ] ReportRenderingEngine `EnsureStorageCreatedAsync` method updated
- [ ] Configuration verified
- [ ] CosmosSeeder alignment verified

### **Testing**
- [ ] Project compiles successfully
- [ ] Application starts without errors
- [ ] Seeding process works
- [ ] No legacy container creation occurs

## 🚨 **Troubleshooting**

### **Compilation Errors**

**Issue**: Missing interface references
**Solution**: Search for and remove all `IReportDataMigrationService` references

**Issue**: Missing using statements
**Solution**: Remove unused imports, add required ones

### **Runtime Errors**

**Issue**: Container not found during seeding
**Solution**: Verify setup script has been run to create containers

**Issue**: Partition key mismatch
**Solution**: Ensure CosmosSeeder uses `/tenantId` partition key

### **Configuration Issues**

**Issue**: Wrong container name
**Solution**: Verify appsettings.json matches setup script configuration

**Issue**: Wrong database name
**Solution**: Ensure `CSHeroReports` is used consistently

## 📞 **Next Steps**

After completing this implementation:

1. **Run full testing suite** (see `legacy-container-cleanup-testing.md`)
2. **Verify container state** in Azure Cosmos DB
3. **Test seeding process** thoroughly
4. **Update documentation** if needed
5. **Monitor application** for any issues

---

**Document Created**: 2025-06-06  
**Implementation Time**: 2-4 hours  
**Complexity**: Medium  
**Risk Level**: Low
