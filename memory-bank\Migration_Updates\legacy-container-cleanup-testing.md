# Legacy Container Creation Cleanup - Testing & Validation Guide

## 📋 **Overview**

This document provides comprehensive testing procedures and validation steps for the legacy container creation cleanup implementation.

**Prerequisites**: Complete implementation steps in `legacy-container-cleanup-implementation.md` before testing.

## 🧪 **Pre-Implementation Testing**

### **Test 1: Document Current State**

**Purpose**: Establish baseline before changes.

**Steps**:
1. **Check Azure Cosmos DB containers**:
   ```bash
   # Using Azure CLI
   az cosmosdb sql container list \
     --account-name [cosmos-account-name] \
     --database-name CSHeroReports \
     --resource-group [resource-group-name]
   ```

2. **Document existing containers**:
   - Container names
   - Partition keys
   - Throughput settings

3. **Test current application startup**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run
   ```

4. **Document current behavior**:
   - Startup logs
   - Container creation messages
   - Seeding process results

### **Test 2: Verify Current Seeding Process**

**Purpose**: Ensure seeding works before changes.

**Steps**:
1. **Run application with logging**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run --verbosity normal
   ```

2. **Check for**:
   - SQL seeding completion
   - Cosmos DB seeding attempts
   - Blob storage container creation
   - Any error messages

3. **Verify data in Cosmos DB**:
   - Check if documents exist
   - Verify partition key usage
   - Confirm document structure

## 🔧 **Post-Implementation Testing**

### **Test 3: Compilation Verification**

**Purpose**: Ensure code compiles after changes.

**Steps**:
1. **Clean and rebuild**:
   ```bash
   cd FY.WB.CSHero2
   dotnet clean
   dotnet build
   ```

2. **Expected Results**:
   - ✅ No compilation errors
   - ✅ No missing reference warnings
   - ✅ All projects build successfully

3. **If errors occur**:
   - Check for missing `IReportDataMigrationService` references
   - Verify all using statements are correct
   - Ensure no orphaned method calls

### **Test 4: Application Startup Testing**

**Purpose**: Verify application starts correctly after changes.

**Steps**:
1. **Start application with detailed logging**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run --verbosity detailed
   ```

2. **Monitor startup logs for**:
   - ✅ No container creation attempts for Cosmos DB
   - ✅ Blob storage container creation only
   - ✅ Seeding process completion
   - ✅ No error messages related to missing services

3. **Expected log messages**:
   ```
   [INFO] CosmosDB containers expected to exist from infrastructure setup
   [INFO] CosmosDB seeding will use existing containers
   [INFO] Ensuring blob storage container is created...
   [INFO] Blob storage container created successfully
   ```

4. **Should NOT see**:
   ```
   [INFO] Ensuring CosmosDB containers are created...
   [INFO] Creating database if it doesn't exist
   [ERROR] IReportDataMigrationService not found
   ```

### **Test 5: Container State Verification**

**Purpose**: Confirm only correct containers exist.

**Steps**:
1. **List Cosmos DB containers**:
   ```bash
   az cosmosdb sql container list \
     --account-name [cosmos-account-name] \
     --database-name CSHeroReports \
     --resource-group [resource-group-name]
   ```

2. **Expected containers**:
   - ✅ `Reports` container with `/tenantId` partition key
   - ❌ NO `report-data` container
   - ❌ NO `report-styles` container

3. **Verify partition key**:
   ```bash
   az cosmosdb sql container show \
     --account-name [cosmos-account-name] \
     --database-name CSHeroReports \
     --name Reports \
     --resource-group [resource-group-name] \
     --query "resource.partitionKey"
   ```

4. **Expected result**:
   ```json
   {
     "paths": ["/tenantId"],
     "kind": "Hash"
   }
   ```

### **Test 6: Seeding Process Validation**

**Purpose**: Ensure seeding still works correctly.

**Steps**:
1. **Clear existing data** (if safe to do so):
   - Delete documents from Cosmos DB
   - Clear SQL seed data

2. **Run seeding process**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run
   ```

3. **Verify seeding results**:
   - ✅ SQL data seeded correctly
   - ✅ Cosmos DB documents created (if seeding enabled)
   - ✅ Blob storage containers created
   - ✅ No partition key errors

4. **Check Cosmos DB documents**:
   ```sql
   SELECT * FROM c WHERE c.tenantId != null
   ```

5. **Verify document structure**:
   - Documents have `tenantId` field
   - Partition key matches document `tenantId`
   - No orphaned documents

## 🔍 **Functional Testing**

### **Test 7: API Endpoint Testing**

**Purpose**: Verify application functionality after changes.

**Steps**:
1. **Test basic endpoints**:
   ```bash
   curl http://localhost:5000/api/ping
   curl http://localhost:5000/api/system-test
   ```

2. **Expected responses**:
   - ✅ Ping returns pong
   - ✅ System test completes successfully
   - ✅ No migration service errors

3. **Test report-related endpoints** (if available):
   ```bash
   curl http://localhost:5000/api/reports
   curl http://localhost:5000/api/templates
   ```

### **Test 8: Database Operations Testing**

**Purpose**: Verify CRUD operations work correctly.

**Steps**:
1. **Test SQL operations**:
   - Create a test report
   - Update report metadata
   - Delete test report

2. **Test Cosmos DB operations** (if seeding enabled):
   - Verify document creation
   - Test document updates
   - Confirm partition key usage

3. **Test Blob storage operations**:
   - Verify container exists
   - Test file upload/download (if implemented)

## 🚨 **Error Scenario Testing**

### **Test 9: Missing Container Handling**

**Purpose**: Verify graceful handling when containers don't exist.

**Steps**:
1. **Temporarily remove Cosmos DB container**:
   ```bash
   az cosmosdb sql container delete \
     --account-name [cosmos-account-name] \
     --database-name CSHeroReports \
     --name Reports \
     --resource-group [resource-group-name] \
     --yes
   ```

2. **Start application**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run
   ```

3. **Expected behavior**:
   - ✅ Application starts successfully
   - ✅ Graceful error handling for missing container
   - ✅ No application crash
   - ⚠️ Warning messages about Cosmos DB functionality

4. **Restore container**:
   ```bash
   # Run setup script to recreate container
   ./scripts/setup-azure-infrastructure.ps1 -ResourceGroupName [rg] -Location [location]
   ```

### **Test 10: Configuration Error Testing**

**Purpose**: Test handling of configuration mismatches.

**Steps**:
1. **Temporarily modify appsettings.json**:
   ```json
   {
     "CosmosDb": {
       "ContainerName": "WrongContainer"
     }
   }
   ```

2. **Start application**:
   ```bash
   cd FY.WB.CSHero2
   dotnet run
   ```

3. **Expected behavior**:
   - ⚠️ Container not found errors
   - ✅ Application continues running
   - ✅ Other functionality works

4. **Restore correct configuration**:
   ```json
   {
     "CosmosDb": {
       "ContainerName": "Reports"
     }
   }
   ```

## 📊 **Performance Testing**

### **Test 11: Startup Performance**

**Purpose**: Verify startup time improvement after removing container creation.

**Steps**:
1. **Measure startup time**:
   ```bash
   time dotnet run --no-build
   ```

2. **Compare with baseline** (from pre-implementation testing)

3. **Expected improvement**:
   - ✅ Faster startup (no container creation overhead)
   - ✅ Reduced Azure API calls
   - ✅ Fewer network operations

### **Test 12: Memory Usage**

**Purpose**: Verify no memory leaks from removed services.

**Steps**:
1. **Monitor memory usage during startup**
2. **Check for**:
   - ✅ No memory leaks
   - ✅ Proper service disposal
   - ✅ Reduced memory footprint

## ✅ **Validation Checklist**

### **Code Quality Validation**
- [ ] No compilation errors
- [ ] No unused imports
- [ ] No orphaned method calls
- [ ] No references to removed services
- [ ] Clean, readable code

### **Functionality Validation**
- [ ] Application starts successfully
- [ ] Seeding process works
- [ ] API endpoints respond correctly
- [ ] Database operations function
- [ ] Error handling works properly

### **Architecture Validation**
- [ ] Single container creation source (setup script)
- [ ] Consistent partition key usage (`/tenantId`)
- [ ] Proper configuration alignment
- [ ] No legacy container creation logic
- [ ] Aligned with current architecture

### **Infrastructure Validation**
- [ ] Only correct containers exist in Cosmos DB
- [ ] Partition keys are correct
- [ ] Blob storage containers created properly
- [ ] No orphaned resources
- [ ] Configuration matches setup script

## 🚨 **Troubleshooting Guide**

### **Common Issues and Solutions**

#### **Issue**: Compilation errors about missing services
**Solution**: 
1. Search for all `IReportDataMigrationService` references
2. Remove from dependency injection
3. Remove from controller constructors

#### **Issue**: Container not found errors during seeding
**Solution**:
1. Verify setup script has been run
2. Check appsettings.json configuration
3. Confirm container name matches setup script

#### **Issue**: Partition key mismatch errors
**Solution**:
1. Verify CosmosSeeder uses `document.TenantId`
2. Check container partition key is `/tenantId`
3. Ensure no legacy `/partitionKey` usage

#### **Issue**: Application won't start
**Solution**:
1. Check for missing using statements
2. Verify all method calls are valid
3. Ensure no circular dependencies

#### **Issue**: Seeding process fails
**Solution**:
1. Check Cosmos DB connection string
2. Verify container exists
3. Check tenant data availability

## 📈 **Success Criteria**

### **Must Have**
- ✅ Application compiles and runs
- ✅ No legacy container creation occurs
- ✅ Seeding process works correctly
- ✅ Only correct containers exist
- ✅ Partition keys are consistent

### **Should Have**
- ✅ Improved startup performance
- ✅ Clean, maintainable code
- ✅ Proper error handling
- ✅ Comprehensive logging

### **Nice to Have**
- ✅ Reduced memory usage
- ✅ Faster seeding process
- ✅ Better monitoring capabilities

## 📞 **Post-Testing Actions**

After successful testing:

1. **Document results** in project notes
2. **Update architecture documentation** if needed
3. **Notify team** of changes
4. **Monitor production** deployment (if applicable)
5. **Schedule follow-up** review

---

**Document Created**: 2025-06-06  
**Testing Time**: 1-2 hours  
**Complexity**: Medium  
**Critical Path**: Yes
