# Multi-Storage Architecture Implementation Plan

## 📋 **Executive Summary**

This document outlines the implementation plan for completing the multi-storage architecture that separates report data across three specialized storage systems:

1. **SQL Database**: Report metadata, versions, and style references
2. **Azure Blob Storage**: Rendered Next.js components and large data files  
3. **Azure Cosmos DB**: Report styles and configuration data

The current system has the infrastructure in place but still uses deprecated `ComponentDefinition` entities that need to be replaced with the multi-storage approach.

## 🎯 **Current State Analysis**

### ✅ **Already Implemented**
- **Domain Entities**: `Report` and `ReportVersion` have multi-storage fields (`DataDocumentId`, `ComponentsBlobId`, `StyleDocumentId`)
- **Infrastructure Services**: 
  - [`AzureBlobReportDataService`](../FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/AzureBlobReportDataService.cs) - Complete blob storage implementation
  - [`CosmosDbReportStyleService`](../FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/CosmosDbReportStyleService.cs) - Complete Cosmos DB implementation
  - [`StorageInitializationService`](../FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Services/StorageInitializationService.cs) - Storage setup and validation
- **Configuration**: Blob and Cosmos DB options classes exist

### ❌ **Missing Components**
- **Repository Interfaces**: Multi-storage repository abstractions
- **Service Orchestration**: Services that coordinate between storage types
- **Component Storage**: Blob storage service for React components
- **Data Migration**: Service to migrate from current SQL-based storage
- **Application Services**: Updated to use multi-storage repositories

### 🚨 **Build Errors (22 errors)**
- **ComponentDefinition References**: Services still reference deprecated entities
- **Navigation Properties**: `.Include(rv => rv.ComponentDefinitions)` statements
- **Entity Creation**: Code creating `ComponentDefinition` instances

## 🏗️ **Implementation Architecture**

### **Phase 1: Repository Layer Implementation**

#### 1.1 Create Repository Interfaces

```csharp
// IReportMetadataRepository - SQL Database
public interface IReportMetadataRepository
{
    Task<Report> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<ReportVersion> GetReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
    Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(Report report, CancellationToken cancellationToken = default);
    Task UpdateReportVersionAsync(ReportVersion version, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task DeleteReportVersionAsync(Guid versionId, CancellationToken cancellationToken = default);
}

// IReportDataRepository - Cosmos DB
public interface IReportDataRepository
{
    Task<ReportDataDocument> GetReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);
    Task<string> CreateReportDataAsync(ReportDataDocument data, CancellationToken cancellationToken = default);
    Task UpdateReportDataAsync(ReportDataDocument data, CancellationToken cancellationToken = default);
    Task DeleteReportDataAsync(string documentId, Guid tenantId, CancellationToken cancellationToken = default);
    Task<ReportSection> GetSectionAsync(string documentId, string sectionId, Guid tenantId, CancellationToken cancellationToken = default);
    Task UpdateSectionAsync(string documentId, ReportSection section, Guid tenantId, CancellationToken cancellationToken = default);
}

// IReportComponentsRepository - Blob Storage
public interface IReportComponentsRepository
{
    Task<ComponentsMetadata> GetComponentsMetadataAsync(string blobId, CancellationToken cancellationToken = default);
    Task<string> SaveComponentsAsync(Guid tenantId, Guid reportId, Guid versionId, ComponentResult components, CancellationToken cancellationToken = default);
    Task<ComponentDefinition> GetComponentAsync(string blobId, string componentId, CancellationToken cancellationToken = default);
    Task<List<ComponentDefinition>> GetAllComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    Task DeleteComponentsAsync(string blobId, CancellationToken cancellationToken = default);
    Task<string> CopyComponentsAsync(string sourceBlobId, Guid tenantId, Guid reportId, Guid versionId, CancellationToken cancellationToken = default);
}
```

#### 1.2 Implement Repository Classes

**Location**: `FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/Repositories/`

- `ReportMetadataRepository.cs` - SQL implementation using Entity Framework
- `ReportDataRepository.cs` - Cosmos DB implementation using existing service
- `ReportComponentsRepository.cs` - Blob Storage implementation for components

### **Phase 2: Domain Models for Multi-Storage**

#### 2.1 Cosmos DB Document Models

```csharp
// ReportDataDocument - Cosmos DB
public class ReportDataDocument
{
    public string Id { get; set; } = string.Empty;
    public string PartitionKey { get; set; } = string.Empty; // TenantId
    public Guid ReportId { get; set; }
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public List<ReportSection> Sections { get; set; } = new();
    public ReportDataMetadata Metadata { get; set; } = new();
}

public class ReportSection
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public int Order { get; set; }
    public List<ReportSectionField> Fields { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public class ReportSectionField
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public int Order { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}
```

#### 2.2 Blob Storage Models

```csharp
// ComponentsMetadata - Blob Storage metadata.json
public class ComponentsMetadata
{
    public Guid ReportId { get; set; }
    public Guid VersionId { get; set; }
    public int VersionNumber { get; set; }
    public List<ComponentMetadata> Components { get; set; } = new();
    public DateTime CreatedAt { get; set; }
    public Guid CreatedBy { get; set; }
}

public class ComponentMetadata
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string SectionId { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public List<string> Imports { get; set; } = new();
    public List<string> Props { get; set; } = new();
    public string ComponentType { get; set; } = string.Empty;
    public string Framework { get; set; } = "NextJS";
    public string StyleFramework { get; set; } = "TailwindCSS";
}

// ComponentDefinition - Individual component file
public class ComponentDefinition
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string SectionId { get; set; } = string.Empty;
    public string ComponentCode { get; set; } = string.Empty;
    public string TypeDefinitions { get; set; } = string.Empty;
    public List<string> Imports { get; set; } = new();
    public Dictionary<string, object> Metadata { get; set; } = new();
    public string ComponentHash { get; set; } = string.Empty;
}
```

### **Phase 3: Service Layer Implementation**

#### 3.1 Orchestration Services

```csharp
// IReportService - Orchestrates all storage types
public interface IReportService
{
    Task<ReportDto> GetReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<Guid> CreateReportAsync(CreateReportRequest request, CancellationToken cancellationToken = default);
    Task UpdateReportAsync(UpdateReportRequest request, CancellationToken cancellationToken = default);
    Task DeleteReportAsync(Guid reportId, CancellationToken cancellationToken = default);
}

// IVersioningService - Multi-storage version management
public interface IVersioningService
{
    Task<ReportVersion> CreateVersionAsync(Guid reportId, ComponentResult components, string? description = null, CancellationToken cancellationToken = default);
    Task<ReportVersion> GetVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);
    Task<ReportVersion> GetCurrentVersionAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<List<ReportVersion>> GetVersionHistoryAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<bool> RollbackToVersionAsync(Guid reportId, int versionNumber, CancellationToken cancellationToken = default);
}

// IReportRenderingService - Component rendering with multi-storage
public interface IReportRenderingService
{
    Task<RenderResult> RenderReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<RenderResult> RenderSectionAsync(Guid reportId, string sectionId, CancellationToken cancellationToken = default);
    Task<ComponentDefinition> GetComponentAsync(Guid reportId, string componentId, CancellationToken cancellationToken = default);
}
```

### **Phase 4: Data Migration Service**

#### 4.1 Migration Strategy

```csharp
public interface IDataMigrationService
{
    Task<MigrationResult> MigrateToMultiStorageAsync(CancellationToken cancellationToken = default);
    Task<MigrationResult> MigrateReportAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<bool> ValidateMigrationAsync(Guid reportId, CancellationToken cancellationToken = default);
    Task<MigrationStatus> GetMigrationStatusAsync(CancellationToken cancellationToken = default);
}
```

**Migration Process**:
1. **Extract Data**: Read existing reports with `ComponentDataJson` and `JsonData`
2. **Create Cosmos Documents**: Store report data in Cosmos DB
3. **Create Blob Components**: Store component definitions in Blob Storage
4. **Update SQL References**: Set `DataDocumentId` and `ComponentsBlobId` in SQL
5. **Validate Migration**: Ensure data integrity across all storage types
6. **Clean Up**: Remove deprecated JSON fields after successful migration

## 📁 **File Structure**

```
FY.WB.CSHero2.ReportRenderingEngine.Domain/
├── Models/
│   ├── ReportDataDocument.cs
│   ├── ComponentsMetadata.cs
│   ├── ComponentDefinition.cs
│   └── MigrationModels.cs
└── Interfaces/
    ├── IReportMetadataRepository.cs
    ├── IReportDataRepository.cs
    ├── IReportComponentsRepository.cs
    └── IDataMigrationService.cs

FY.WB.CSHero2.ReportRenderingEngine.Infrastructure/
├── Repositories/
│   ├── ReportMetadataRepository.cs
│   ├── ReportDataRepository.cs
│   └── ReportComponentsRepository.cs
└── Services/
    └── DataMigrationService.cs

FY.WB.CSHero2.ReportRenderingEngine.Application/
└── Services/
    ├── ReportServiceImpl.cs (Updated)
    ├── VersioningServiceImpl.cs (Updated)
    ├── ReportRenderingServiceImpl.cs (Updated)
    └── ComponentGeneratorImpl.cs (Updated)
```

## 🔧 **Implementation Steps**

### **Step 1: Create Domain Models and Interfaces**
1. Create `ReportDataDocument.cs` and related models
2. Create `ComponentsMetadata.cs` and `ComponentDefinition.cs`
3. Create repository interfaces
4. Create migration service interface

### **Step 2: Implement Repository Layer**
1. Implement `ReportMetadataRepository` using Entity Framework
2. Implement `ReportDataRepository` using Cosmos DB
3. Implement `ReportComponentsRepository` using Blob Storage
4. Add repository registrations to DI container

### **Step 3: Implement Data Migration Service**
1. Create `DataMigrationService` to migrate existing data
2. Implement migration validation and rollback
3. Create migration controller endpoint
4. Add comprehensive logging and error handling

### **Step 4: Update Application Services**
1. Update `VersioningServiceImpl` to use multi-storage repositories
2. Update `VersionComparisonService` to work with new storage
3. Update `EnhancedVersioningService` for multi-storage
4. Update `ReportRendererV2` and `ReportServiceImpl`
5. Update `ComponentGeneratorImpl` to save to blob storage

### **Step 5: Remove Deprecated Code**
1. Remove all `ComponentDefinition` entity references
2. Remove `.Include(rv => rv.ComponentDefinitions)` statements
3. Remove `ComponentDataJson` and `JsonData` usage after migration
4. Update database migration to remove deprecated fields

### **Step 6: Testing and Validation**
1. Create integration tests for each repository
2. Test data migration with sample data
3. Validate cross-storage data consistency
4. Performance testing for multi-storage operations

## 🧪 **Testing Strategy**

### **Unit Tests**
- Repository implementations with mocked dependencies
- Service layer with mocked repositories
- Data migration logic with test data

### **Integration Tests**
- End-to-end multi-storage operations
- Data migration validation
- Cross-storage data consistency
- Performance benchmarks

### **Migration Testing**
- Backup and restore procedures
- Migration rollback scenarios
- Data integrity validation
- Large dataset migration testing

## 📊 **Success Criteria**

### **Functional Requirements**
- ✅ All build errors resolved
- ✅ Report creation works with multi-storage
- ✅ Version management preserves all functionality
- ✅ Component rendering works from blob storage
- ✅ Data migration completes successfully
- ✅ Rollback capabilities maintained

### **Performance Requirements**
- ✅ Report loading time < 2 seconds
- ✅ Component rendering time < 1 second
- ✅ Version comparison time < 3 seconds
- ✅ Migration throughput > 100 reports/minute

### **Quality Requirements**
- ✅ 100% test coverage for new code
- ✅ Zero data loss during migration
- ✅ Backward compatibility during transition
- ✅ Comprehensive error handling and logging

## 🚨 **Risk Mitigation**

| Risk | Impact | Mitigation |
|------|--------|------------|
| Data loss during migration | High | Comprehensive backup strategy, rollback procedures |
| Performance degradation | Medium | Caching layer, optimized queries, batch operations |
| Storage cost increase | Medium | Monitor usage, implement data lifecycle policies |
| Complex debugging | Medium | Comprehensive logging, monitoring dashboards |
| Migration failures | High | Incremental migration, validation at each step |

## 📅 **Timeline Estimate**

| Phase | Duration | Dependencies |
|-------|----------|--------------|
| Phase 1: Repository Layer | 3-4 days | Domain models, interfaces |
| Phase 2: Domain Models | 1-2 days | Architecture review |
| Phase 3: Service Layer | 4-5 days | Repository implementations |
| Phase 4: Data Migration | 3-4 days | All repositories, services |
| Phase 5: Testing & Validation | 2-3 days | Complete implementation |
| **Total** | **13-18 days** | |

## 🔄 **Rollback Plan**

1. **Immediate Rollback**: Revert to using `ComponentDataJson` fields
2. **Data Restoration**: Restore from SQL backup if needed
3. **Service Rollback**: Switch back to original service implementations
4. **Validation**: Ensure all functionality works with original approach

## 📞 **Next Steps**

1. **Review and Approve**: Architecture and implementation plan
2. **Create Tasks**: Break down into specific development tasks
3. **Set Up Environment**: Ensure Cosmos DB and Blob Storage are configured
4. **Begin Implementation**: Start with Phase 1 (Repository Layer)
5. **Continuous Testing**: Test each phase before proceeding

---

**Document Created**: 2025-06-05 22:40  
**Status**: Ready for Implementation  
**Priority**: High - Resolves build errors and implements target architecture