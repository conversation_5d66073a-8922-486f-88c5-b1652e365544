# DataSeeder Implementation Plan for Multi-Storage Architecture

## Overview

This document outlines the comprehensive plan to update the DataSeeder implementation to work with the optimized SQL structure and multi-storage architecture, supporting the draft-based editing workflow and template inheritance model.

## Current DataSeeder Analysis

### Existing Structure
1. **SqlSeeder**: Handles SQL entities (Reports, ReportSections, ReportSectionFields, etc.)
2. **CosmosSeeder**: Migrates SQL data to Cosmos DB documents
3. **BlobSeeder**: Placeholder for blob storage seeding
4. **SeedingCoordinator**: Orchestrates multi-storage seeding

### Issues with Current Implementation
1. **Data Duplication**: Creates both SQL and Cosmos DB data simultaneously
2. **Inconsistent References**: Storage references not properly linked
3. **Missing Blob Storage**: No actual blob storage seeding implementation
4. **No Draft Support**: Doesn't handle draft-based editing workflow
5. **No Template Inheritance**: Missing template source tracking

## Updated DataSeeder Architecture

### Phase 1: Enhanced SqlSeeder Implementation

#### 1.1 Updated SqlSeeder Structure

```csharp
public class SqlSeeder : ISqlSeeder
{
    private readonly ILogger<SqlSeeder> _logger;
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting SQL Server seeding with multi-storage support...");

        try
        {
            var tableStatus = await GetTableStatusAsync(context);
            LogTableStatus(tableStatus);

            // Phase 1: Independent entities
            await SeedTenantProfilesAsync(context, tableStatus.TenantProfilesEmpty, cancellationToken);
            await SeedTemplatesAsync(context, tableStatus.TemplatesEmpty, cancellationToken);
            
            // Phase 2: Entities that depend on TenantProfiles
            await SeedClientsAsync(context, tableStatus.ClientsEmpty, cancellationToken);
            
            // Phase 3: Reports with new structure
            await SeedReportsAsync(context, tableStatus.ReportsEmpty, cancellationToken);
            
            // Phase 4: Report versions with storage references
            await SeedReportVersionsAsync(context, tableStatus.ReportVersionsEmpty, cancellationToken);
            
            // Phase 5: Report storage metadata
            await SeedReportStorageMetadataAsync(context, tableStatus.ReportStorageMetadataEmpty, cancellationToken);
            
            // Phase 6: Report styles (keep in SQL for now)
            await SeedReportStylesAsync(context, tableStatus.ReportStylesEmpty, cancellationToken);
            
            // Phase 7: Sections and fields with migration tracking
            await SeedReportSectionsAsync(context, tableStatus.ReportSectionsEmpty, cancellationToken);
            await SeedReportSectionFieldsAsync(context, tableStatus.ReportSectionFieldsEmpty, cancellationToken);

            _logger.LogInformation("SQL Server seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during SQL Server seeding");
            throw;
        }
    }

    private async Task SeedReportsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
    {
        if (!isEmpty)
        {
            _logger.LogInformation("Skipping Reports seeding - table has data");
            return;
        }

        _logger.LogInformation("Seeding Reports with new structure...");
        var reportDtos = await ReadJsonData<ReportSeedDto>("reports.json");
        var allClients = await context.Clients.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var validReports = new List<Report>();
        var skippedCount = 0;

        foreach (var reportDto in reportDtos)
        {
            var client = allClients.FirstOrDefault(c => 
                c.CompanyName == reportDto.ClientName && 
                c.TenantId == reportDto.TenantId);

            if (client == null)
            {
                _logger.LogWarning("Skipping Report '{ReportName}' - Client '{ClientName}' not found for TenantId {TenantId}",
                    reportDto.Name, reportDto.ClientName, reportDto.TenantId);
                skippedCount++;
                continue;
            }

            var report = new Report(
                reportDto.Id, reportDto.ReportNumber, client.Id, reportDto.ClientName,
                reportDto.Name, reportDto.Category, reportDto.SlideCount, reportDto.Status, reportDto.Author);

            // Set new properties for multi-storage
            report.TenantId = reportDto.TenantId;
            report.IsDraft = false; // Seed data represents saved reports
            report.LastSavedAt = reportDto.CreationTime;
            report.CreationTime = reportDto.CreationTime == default ? DateTime.UtcNow : reportDto.CreationTime;
            report.LastModificationTime = reportDto.LastModificationTime ?? DateTime.UtcNow;
            report.IsDeleted = reportDto.IsDeleted;

            validReports.Add(report);
        }

        await SeedEntities(context, validReports, "Report", cancellationToken);
        if (skippedCount > 0)
        {
            _logger.LogWarning("Skipped {SkippedCount} reports due to missing client references", skippedCount);
        }
    }

    private async Task SeedReportVersionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
    {
        if (!isEmpty)
        {
            _logger.LogInformation("Skipping ReportVersions seeding - table has data");
            return;
        }

        _logger.LogInformation("Seeding ReportVersions with storage references...");
        var versionDtos = await ReadJsonData<ReportVersionSeedDto>("report-versions.json");
        var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var validVersions = new List<ReportVersion>();
        var skippedCount = 0;

        foreach (var versionDto in versionDtos)
        {
            var reportExists = allReports.Any(r => r.Id == versionDto.ReportId);

            if (!reportExists)
            {
                _logger.LogWarning("Skipping ReportVersion - Report not found");
                skippedCount++;
                continue;
            }

            var version = new ReportVersion
            {
                Id = versionDto.Id,
                ReportId = versionDto.ReportId,
                VersionNumber = versionDto.VersionNumber,
                Description = versionDto.Description,
                IsCurrent = versionDto.IsCurrent,
                CreatedAt = versionDto.CreationTime,
                
                // Storage references (will be populated by other seeders)
                DataDocumentId = null, // Will be set by CosmosSeeder
                ComponentsBlobId = null, // Will be set by BlobSeeder
                StylesBlobId = null, // Will be set by BlobSeeder
                
                // Storage strategy
                StorageStrategy = "SQL", // Start with SQL, upgrade during migration
                
                // Size tracking (will be calculated during migration)
                DataSize = 0,
                ComponentsSize = 0,
                StylesSize = 0,
                
                CreationTime = versionDto.CreationTime,
                LastModificationTime = versionDto.LastModificationTime
            };

            validVersions.Add(version);
        }

        await SeedEntities(context, validVersions, "ReportVersion", cancellationToken);
        
        // Update reports with current version references
        await UpdateReportsWithCurrentVersionsAsync(context, cancellationToken);
        
        if (skippedCount > 0)
        {
            _logger.LogWarning("Skipped {SkippedCount} report versions due to missing report references", skippedCount);
        }
    }

    private async Task SeedReportStorageMetadataAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
    {
        if (!isEmpty)
        {
            _logger.LogInformation("Skipping ReportStorageMetadata seeding - table has data");
            return;
        }

        _logger.LogInformation("Seeding ReportStorageMetadata...");
        var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var metadataEntities = new List<ReportStorageMetadata>();

        foreach (var report in allReports)
        {
            var metadata = new ReportStorageMetadata
            {
                Id = Guid.NewGuid(),
                ReportId = report.Id,
                StorageStrategy = "SQL",
                MigrationStatus = "NotMigrated",
                SqlStorageSize = 1024, // Placeholder - will be calculated
                CosmosStorageSize = 0,
                BlobStorageSize = 0,
                TotalStorageSize = 1024,
                AccessCount = 0,
                CreationTime = DateTime.UtcNow,
                LastModificationTime = DateTime.UtcNow
            };

            metadataEntities.Add(metadata);
        }

        await SeedEntities(context, metadataEntities, "ReportStorageMetadata", cancellationToken);
    }

    private async Task SeedReportSectionsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
    {
        if (!isEmpty)
        {
            _logger.LogInformation("Skipping ReportSections seeding - table has data");
            return;
        }

        _logger.LogInformation("Seeding ReportSections with migration tracking...");
        var sectionDtos = await ReadJsonData<ReportSectionSeedDto>("report-sections.json");
        var allReports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var validSections = new List<ReportSection>();
        var skippedCount = 0;

        foreach (var sectionDto in sectionDtos)
        {
            var reportExists = allReports.Any(r => r.Id == sectionDto.ReportId);

            if (!reportExists)
            {
                _logger.LogWarning("Skipping ReportSection '{SectionId}' - Report '{ReportId}' not found",
                    sectionDto.Id, sectionDto.ReportId);
                skippedCount++;
                continue;
            }

            var section = new ReportSection
            {
                Id = sectionDto.Id,
                ReportId = sectionDto.ReportId,
                Title = sectionDto.Title,
                Type = sectionDto.Type,
                Order = sectionDto.Order,
                
                // Migration tracking
                IsMigratedToCosmos = false,
                CosmosDocumentId = null,
                MigrationDate = null,
                
                // Template inheritance (if applicable)
                TemplateSourceSectionId = sectionDto.TemplateSourceSectionId,
                IsModifiedFromTemplate = sectionDto.IsModifiedFromTemplate,
                
                CreationTime = sectionDto.CreationTime,
                LastModificationTime = sectionDto.LastModificationTime
            };

            validSections.Add(section);
        }

        await SeedEntities(context, validSections, "ReportSection", cancellationToken);
        if (skippedCount > 0)
        {
            _logger.LogWarning("Skipped {SkippedCount} report sections due to missing report references", skippedCount);
        }
    }

    private async Task SeedReportSectionFieldsAsync(ApplicationDbContext context, bool isEmpty, CancellationToken cancellationToken)
    {
        if (!isEmpty)
        {
            _logger.LogInformation("Skipping ReportSectionFields seeding - table has data");
            return;
        }

        _logger.LogInformation("Seeding ReportSectionFields with migration tracking...");
        var fieldDtos = await ReadJsonData<ReportSectionFieldSeedDto>("report-section-fields.json");
        var allSections = await context.ReportSections.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var validFields = new List<ReportSectionField>();
        var skippedCount = 0;

        foreach (var fieldDto in fieldDtos)
        {
            var sectionExists = allSections.Any(s => s.Id == fieldDto.SectionId);

            if (!sectionExists)
            {
                _logger.LogWarning("Skipping ReportSectionField '{FieldId}' - Section '{SectionId}' not found",
                    fieldDto.Id, fieldDto.SectionId);
                skippedCount++;
                continue;
            }

            var field = new ReportSectionField
            {
                Id = fieldDto.Id,
                SectionId = fieldDto.SectionId,
                Name = fieldDto.Name,
                Type = fieldDto.Type,
                Content = fieldDto.Content,
                Order = fieldDto.Order,
                
                // Migration tracking
                IsMigratedToCosmos = false,
                MigrationDate = null,
                
                // Template inheritance (if applicable)
                TemplateSourceFieldId = fieldDto.TemplateSourceFieldId,
                IsModifiedFromTemplate = fieldDto.IsModifiedFromTemplate,
                
                CreationTime = fieldDto.CreationTime,
                LastModificationTime = fieldDto.LastModificationTime
            };

            validFields.Add(field);
        }

        await SeedEntities(context, validFields, "ReportSectionField", cancellationToken);
        if (skippedCount > 0)
        {
            _logger.LogWarning("Skipped {SkippedCount} report section fields due to missing section references", skippedCount);
        }
    }

    private async Task UpdateReportsWithCurrentVersionsAsync(ApplicationDbContext context, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating reports with current version references...");
        
        var reports = await context.Reports.IgnoreQueryFilters().ToListAsync(cancellationToken);
        var versions = await context.ReportVersions.IgnoreQueryFilters().ToListAsync(cancellationToken);
        
        foreach (var report in reports)
        {
            var currentVersion = versions.FirstOrDefault(v => v.ReportId == report.Id && v.IsCurrent);
            if (currentVersion != null)
            {
                report.CurrentVersionId = currentVersion.Id;
            }
        }
        
        await context.SaveChangesAsync(cancellationToken);
    }

    // DTO classes for new structure
    public class ReportSeedDto
    {
        public Guid Id { get; set; }
        public string ReportNumber { get; set; } = string.Empty;
        public Guid ClientId { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SlideCount { get; set; }
        public string Status { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public Guid? TenantId { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
        public bool IsDeleted { get; set; }
    }

    public class ReportVersionSeedDto
    {
        public Guid Id { get; set; }
        public Guid ReportId { get; set; }
        public int VersionNumber { get; set; }
        public string Description { get; set; } = string.Empty;
        public bool IsCurrent { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }

    public class ReportSectionSeedDto
    {
        public Guid Id { get; set; }
        public Guid ReportId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int Order { get; set; }
        public Guid? TemplateSourceSectionId { get; set; }
        public bool IsModifiedFromTemplate { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }

    public class ReportSectionFieldSeedDto
    {
        public Guid Id { get; set; }
        public Guid SectionId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public int Order { get; set; }
        public Guid? TemplateSourceFieldId { get; set; }
        public bool IsModifiedFromTemplate { get; set; }
        public DateTime CreationTime { get; set; }
        public DateTime? LastModificationTime { get; set; }
    }
}
```

### Phase 2: Enhanced CosmosSeeder Implementation

#### 2.1 Version-Aware CosmosSeeder

```csharp
public class CosmosSeeder : ICosmosSeeder
{
    private readonly ICosmosDbService? _cosmosDbService;
    private readonly ILogger<CosmosSeeder> _logger;

    public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Cosmos DB seeding with version support...");

        if (_cosmosDbService == null)
        {
            _logger.LogWarning("Cosmos DB service not available, skipping Cosmos DB seeding");
            return;
        }

        try
        {
            // Get existing document IDs to avoid duplicates
            var existingDocumentIds = await GetExistingDocumentIdsAsync(cancellationToken);
            _logger.LogInformation("Found {ExistingCount} existing documents in Cosmos DB", existingDocumentIds.Count);

            // Get all reports with their current versions and sections/fields
            var reportsWithData = await GetReportsWithVersionDataAsync(context, cancellationToken);
            _logger.LogInformation("Found {ReportCount} reports to process for Cosmos DB seeding", reportsWithData.Count);

            var createdCount = 0;
            var skippedCount = 0;
            var errorCount = 0;

            foreach (var reportData in reportsWithData)
            {
                try
                {
                    var documentId = GenerateVersionedDocumentId(reportData.Report.Id, reportData.Version.Id);
                    
                    if (existingDocumentIds.Contains(documentId))
                    {
                        _logger.LogDebug("Skipping report {ReportId} version {VersionId} - document already exists", 
                            reportData.Report.Id, reportData.Version.Id);
                        skippedCount++;
                        continue;
                    }

                    // Create versioned document from report data
                    var document = await CreateVersionedReportDocumentAsync(reportData, cancellationToken);
                    
                    // Validate partition key
                    if (string.IsNullOrEmpty(document.TenantId))
                    {
                        _logger.LogError("Cannot seed report {ReportId} - TenantId is null or empty", reportData.Report.Id);
                        errorCount++;
                        continue;
                    }
                    
                    // Save to Cosmos DB using document's TenantId as partition key
                    await _cosmosDbService.UpsertItemAsync(document, document.TenantId);
                    
                    // Update SQL ReportVersion with document ID
                    await UpdateReportVersionWithDocumentIdAsync(context, reportData.Version.Id, documentId, cancellationToken);
                    
                    // Update storage metadata
                    await UpdateStorageMetadataAsync(context, reportData.Report.Id, "CosmosDB", document, cancellationToken);
                    
                    createdCount++;
                    _logger.LogDebug("Created Cosmos document for report {ReportId} version {VersionId} with partition key {PartitionKey}", 
                        reportData.Report.Id, reportData.Version.Id, document.TenantId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error seeding report {ReportId} version {VersionId} to Cosmos DB", 
                        reportData.Report.Id, reportData.Version?.Id);
                    errorCount++;
                }
            }

            _logger.LogInformation("Cosmos DB seeding completed: {Created} created, {Skipped} skipped, {Errors} errors", 
                createdCount, skippedCount, errorCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error during Cosmos DB seeding");
            throw;
        }
    }

    private async Task<List<ReportWithVersionData>> GetReportsWithVersionDataAsync(ApplicationDbContext context, CancellationToken cancellationToken)
    {
        var reports = await context.Reports
            .IgnoreQueryFilters()
            .Include(r => r.Versions.Where(v => v.IsCurrent))
            .ToListAsync(cancellationToken);

        var result = new List<ReportWithVersionData>();

        foreach (var report in reports)
        {
            var currentVersion = report.Versions.FirstOrDefault(v => v.IsCurrent);
            if (currentVersion == null) continue;

            var sections = await context.ReportSections
                .IgnoreQueryFilters()
                .Where(rs => rs.ReportId == report.Id)
                .Include(rs => rs.Fields)
                .ToListAsync(cancellationToken);

            result.Add(new ReportWithVersionData
            {
                Report = report,
                Version = currentVersion,
                Sections = sections
            });
        }

        return result;
    }

    private async Task<VersionedReportDataDocument> CreateVersionedReportDocumentAsync(
        ReportWithVersionData reportData, 
        CancellationToken cancellationToken)
    {
        var tenantId = reportData.Report.TenantId?.ToString() ?? "default";
        if (string.IsNullOrEmpty(tenantId) || tenantId == "00000000-0000-0000-0000-000000000000")
        {
            tenantId = "default";
            _logger.LogWarning("Report {ReportId} has null/empty TenantId, using 'default'", reportData.Report.Id);
        }

        // Transform sections and fields into document format
        var sections = reportData.Sections?.Select(section => new ReportDataSection
        {
            Id = section.Id,
            Name = section.Title,
            Title = section.Title,
            Description = string.Empty,
            SectionType = section.Type,
            DisplayOrder = section.Order,
            IsRequired = false,
            TemplateSourceSectionId = section.TemplateSourceSectionId,
            IsModifiedFromTemplate = section.IsModifiedFromTemplate,
            Fields = section.Fields?.Select(field => new ReportDataField
            {
                Id = field.Id,
                Name = field.Name,
                Label = field.Name,
                FieldType = field.Type,
                DefaultValue = field.Content ?? string.Empty,
                IsRequired = false,
                DisplayOrder = field.Order,
                TemplateSourceFieldId = field.TemplateSourceFieldId,
                IsModifiedFromTemplate = field.IsModifiedFromTemplate,
                ValidationRules = new Dictionary<string, object>(),
                Options = new List<string>()
            }).ToList() ?? new List<ReportDataField>()
        }).ToList() ?? new List<ReportDataSection>();

        return new VersionedReportDataDocument
        {
            Id = GenerateVersionedDocumentId(reportData.Report.Id, reportData.Version.Id),
            TenantId = tenantId,
            ReportId = reportData.Report.Id,
            VersionId = reportData.Version.Id,
            VersionNumber = reportData.Version.VersionNumber,
            ReportName = reportData.Report.Name,
            ReportNumber = reportData.Report.ReportNumber,
            Category = reportData.Report.Category,
            Status = reportData.Report.Status,
            Author = reportData.Report.Author,
            ClientId = reportData.Report.ClientId,
            ClientName = reportData.Report.ClientName,
            IsDraft = reportData.Report.IsDraft,
            Sections = sections,
            Metadata = new ReportDataMetadata
            {
                CreatedAt = reportData.Report.CreationTime,
                UpdatedAt = reportData.Report.LastModificationTime ?? reportData.Report.CreationTime,
                SectionCount = sections.Count,
                FieldCount = sections.Sum(s => s.Fields.Count),
                Version = reportData.Version.VersionNumber.ToString(),
                Tags = new List<string> { reportData.Report.Category, reportData.Report.Status }
            }
        };
    }

    private async Task UpdateReportVersionWithDocumentIdAsync(ApplicationDbContext context, Guid versionId, string documentId, CancellationToken cancellationToken)
    {
        var version = await context.ReportVersions
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(v => v.Id == versionId, cancellationToken);

        if (version != null)
        {
            version.DataDocumentId = documentId;
            version.StorageStrategy = "Hybrid"; // SQL + Cosmos DB
            await context.SaveChangesAsync(cancellationToken);
        }
    }

    private async Task UpdateStorageMetadataAsync(ApplicationDbContext context, Guid reportId, string storageType, 
        VersionedReportDataDocument document, CancellationToken cancellationToken)
    {
        var metadata = await context.ReportStorageMetadata
            .FirstOrDefaultAsync(m => m.ReportId == reportId, cancellationToken);

        if (metadata != null)
        {
            var documentSize = System.Text.Encoding.UTF8.GetByteCount(JsonSerializer.Serialize(document));
            metadata.CosmosStorageSize = documentSize;
            metadata.TotalStorageSize = metadata.SqlStorageSize + metadata.CosmosStorageSize + metadata.BlobStorageSize;
            metadata.StorageStrategy = "Hybrid";
            metadata.MigrationStatus = "InProgress";
            metadata.MigrationStartDate = DateTime.UtcNow;
            await context.SaveChangesAsync(cancellationToken);
        }
    }

    private static string GenerateVersionedDocumentId(Guid reportId, Guid versionId)
    {
        return $"report-data-{reportId}-v{versionId}";
    }

    // Enhanced document models
    public class VersionedReportDataDocument
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;
        
        [JsonPropertyName("tenantId")]
        public string TenantId { get; set; } = string.Empty;
        
        [JsonPropertyName("reportId")]
        public Guid ReportId { get; set; }
        
        [JsonPropertyName("versionId")]
        public Guid VersionId { get; set; }
        
        [JsonPropertyName("versionNumber")]
        public int VersionNumber { get; set; }
        
        [JsonPropertyName("reportName")]
        public string ReportName { get; set; } = string.Empty;
        
        [JsonPropertyName("reportNumber")]
        public string ReportNumber { get; set; } = string.Empty;
        
        [JsonPropertyName("category")]
        public string Category { get; set; } = string.Empty;
        
        [JsonPropertyName("status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonPropertyName("author")]
        public string Author { get; set; } = string.Empty;
        
        [JsonPropertyName("clientId")]
        public Guid ClientId { get; set; }
        
        [JsonPropertyName("clientName")]
        public string ClientName { get; set; } = string.Empty;
        
        [JsonPropertyName("isDraft")]
        public bool IsDraft { get; set; }
        
        [JsonPropertyName("sections")]
        public List<ReportDataSection> Sections { get; set; } = new();
        
        [JsonPropertyName("metadata")]
        public ReportDataMetadata Metadata { get; set; } = new();
    }

    public class ReportDataSection
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string SectionType { get; set; } = string.Empty;
        public int DisplayOrder { get; set; }
        public bool IsRequired { get; set; }
        public Guid? TemplateSourceSectionId { get; set; }
        public bool IsModifiedFromTemplate { get; set; }
        public List<ReportDataField> Fields { get; set; } = new();
    }

    public class ReportDataField
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public string FieldType { get; set; } = string.Empty;
        public string DefaultValue { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public int DisplayOrder { get; set; }
        public Guid? TemplateSourceFieldId { get; set; }
        public bool IsModifiedFromTemplate { get; set; }
        public Dictionary<string, object> ValidationRules { get; set; } = new();
        public List<string> Options { get; set; } = new();
    }

    public class ReportWithVersionData
    {
        public Report Report { get; set; } = null!;
        public ReportVersion Version { get; set; } = null!;
        public List<ReportSection> Sections { get; set; } = new();
    }
}
```

### Phase 3: New BlobSeeder Implementation

#### 3.1 BlobSeeder for Versioned Styles and Components

```csharp
public class BlobSeeder : IBlobSeeder
{
    private readonly IBlobStorageService? _blobStorageService;
    private readonly ILogger<BlobSeeder> _logger;

    public async Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting Blob Storage seeding...");

        if (_blobStorageService == null)
        {
            _logger.LogWarning("Blob Storage service not available, skipping Blob Storage seeding");
            return;
        }

        try
        {
            // Get existing blob IDs to avoid duplicates
            var existingBlobIds = await GetExistingBlobIdsAsync(cancellationToken);
            _logger.LogInformation("Found {ExistingCount} existing blobs in storage", existingBlobIds.Count);

            // Get all report versions that need blob storage
            var reportVersions = await GetReportVersionsForBlobSeedingAsync(context, cancellationToken);
            _logger.LogInformation("Found {VersionCount} report versions to process for Blob Storage seeding", reportVersions.Count);

            var createdCount = 0;
            var skippedCount = 0;
            var errorCount = 0;

            foreach (var version in reportVersions)
            {
                try
                {
                    var tenantId = version.Report.TenantId?.ToString() ?? "default";

                    // Create components blob
                    var componentsBlob = await CreateComponentsBlobAsync(context, version, cancellationToken);
                    var componentsBlobId = $"components-{version.ReportId}-v{version.Id}";
                    var componentsBlobPath = $"tenants/{tenantId}/reports/{version.ReportId}/versions/v{version.VersionNumber}/components.json";

                    await _blobStorageService.UploadAsync(componentsBlobPath,
                        JsonSerializer.Serialize(componentsBlob, JsonOptions),
                        "application/json", cancellationToken);

                    // Create styles blob
                    var stylesBlob = await CreateStylesBlobAsync(context, version, cancellationToken);
                    var stylesBlobId = $"styles-{version.ReportId}-v{version.Id}";
                    var stylesBlobPath = $"tenants/{tenantId}/reports/{version.ReportId}/versions/v{version.VersionNumber}/styles.json";

                    await _blobStorageService.UploadAsync(stylesBlobPath,
                        JsonSerializer.Serialize(stylesBlob, JsonOptions),
                        "application/json", cancellationToken);

                    // Update SQL with blob references
                    await UpdateReportVersionWithBlobIdsAsync(context, version.Id, componentsBlobId, stylesBlobId, cancellationToken);

                    createdCount++;
                    _logger.LogDebug("Created blobs for report {ReportId} version {VersionNumber}",
                        version.ReportId, version.VersionNumber);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating blobs for report {ReportId} version {VersionNumber}",
                        version.ReportId, version.VersionNumber);
                    errorCount++;
                }
            }

            _logger.LogInformation("Report version blob seeding completed: {Created} created, {Errors} errors",
                createdCount, errorCount);
        }

        private async Task<ComponentsBlob> CreateComponentsBlobAsync(ApplicationDbContext context, ReportVersion version, CancellationToken cancellationToken)
        {
            var sections = await context.ReportSections
                .IgnoreQueryFilters()
                .Where(rs => rs.ReportId == version.ReportId)
                .Include(rs => rs.Fields)
                .OrderBy(rs => rs.Order)
                .ToListAsync(cancellationToken);

            var components = sections.Select(section => new ComponentSection
            {
                Id = section.Id,
                Type = section.Type,
                Component = new ComponentDefinition
                {
                    Name = $"{section.Title.Replace(" ", "")}Section",
                    Props = CreateComponentProps(section),
                    Jsx = GenerateComponentJsx(section),
                    Dependencies = GetComponentDependencies(section.Type)
                },
                TemplateSourceId = section.TemplateSourceSectionId?.ToString(),
                IsModifiedFromTemplate = section.IsModifiedFromTemplate
            }).ToList();

            return new ComponentsBlob
            {
                Id = $"components-{version.ReportId}-v{version.Id}",
                ReportId = version.ReportId,
                VersionId = version.Id,
                VersionNumber = version.VersionNumber,
                TenantId = version.Report.TenantId?.ToString() ?? "default",
                CreatedAt = version.CreationTime,
                UpdatedAt = version.LastModificationTime ?? version.CreationTime,
                Components = new ComponentsData
                {
                    Layout = new LayoutConfig
                    {
                        Type = "standard",
                        Framework = "NextJS",
                        TypeScript = true,
                        StyleFramework = "TailwindCSS"
                    },
                    Sections = components,
                    Metadata = new ComponentMetadata
                    {
                        TotalComponents = components.Count,
                        Framework = "NextJS",
                        Dependencies = components.SelectMany(c => c.Component.Dependencies).Distinct().ToList(),
                        BuildVersion = "1.0.0",
                        LastBuilt = DateTime.UtcNow
                    }
                }
            };
        }

        private async Task<StylesBlob> CreateStylesBlobAsync(ApplicationDbContext context, ReportVersion version, CancellationToken cancellationToken)
        {
            // Get report style if exists
            var reportStyle = await context.ReportStyles
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(rs => rs.ReportId == version.ReportId, cancellationToken);

            var stylesData = reportStyle != null ?
                CreateStylesFromReportStyle(reportStyle) :
                CreateDefaultStyles();

            return new StylesBlob
            {
                Id = $"styles-{version.ReportId}-v{version.Id}",
                ReportId = version.ReportId,
                VersionId = version.Id,
                VersionNumber = version.VersionNumber,
                TenantId = version.Report.TenantId?.ToString() ?? "default",
                CreatedAt = version.CreationTime,
                UpdatedAt = version.LastModificationTime ?? version.CreationTime,
                Styles = stylesData
            };
        }

        private async Task UpdateReportVersionWithBlobIdsAsync(ApplicationDbContext context, Guid versionId,
            string componentsBlobId, string stylesBlobId, CancellationToken cancellationToken)
        {
            var version = await context.ReportVersions
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(v => v.Id == versionId, cancellationToken);

            if (version != null)
            {
                version.ComponentsBlobId = componentsBlobId;
                version.StylesBlobId = stylesBlobId;
                version.StorageStrategy = "MultiStorage"; // SQL + Cosmos DB + Blob Storage

                // Calculate sizes (placeholder - would be actual blob sizes)
                version.ComponentsSize = 2048; // Placeholder
                version.StylesSize = 1024; // Placeholder

                await context.SaveChangesAsync(cancellationToken);
            }
        }

        // Helper methods for component and style creation
        private Dictionary<string, object> CreateComponentProps(ReportSection section)
        {
            var props = new Dictionary<string, object>
            {
                ["heading"] = section.Title,
                ["sectionType"] = section.Type,
                ["className"] = "mb-6 p-4 bg-white rounded-lg shadow"
            };

            // Add field data as props
            if (section.Fields?.Any() == true)
            {
                props["fields"] = section.Fields.Select(f => new
                {
                    name = f.Name,
                    type = f.Type,
                    content = f.Content,
                    order = f.Order
                }).ToList();
            }

            return props;
        }

        private string GenerateComponentJsx(ReportSection section)
        {
            var className = "mb-6 p-4 bg-white rounded-lg shadow";
            var jsx = $@"<div className=""{className}"">
  <h3 className=""text-lg font-semibold mb-3"">{section.Title}</h3>";

            if (section.Fields?.Any() == true)
            {
                foreach (var field in section.Fields.OrderBy(f => f.Order))
                {
                    jsx += $@"
  <div className=""mb-2"">
    <label className=""block text-sm font-medium text-gray-700"">{field.Name}</label>
    <div className=""mt-1"">{field.Content}</div>
  </div>";
                }
            }

            jsx += @"
</div>";

            return jsx;
        }

        private List<string> GetComponentDependencies(string sectionType)
        {
            var baseDependencies = new List<string> { "react", "@types/react" };

            return sectionType.ToLower() switch
            {
                "chart" => baseDependencies.Concat(new[] { "recharts", "d3" }).ToList(),
                "table" => baseDependencies.Concat(new[] { "@tanstack/react-table" }).ToList(),
                "form" => baseDependencies.Concat(new[] { "react-hook-form", "@hookform/resolvers" }).ToList(),
                _ => baseDependencies
            };
        }

        private StylesData CreateDefaultStyles()
        {
            return new StylesData
            {
                Theme = new ThemeInfo
                {
                    Name = "Default",
                    Description = "Default report styling",
                    BaseTheme = "corporate"
                },
                Layout = new LayoutConfig
                {
                    PageSize = "A4",
                    Orientation = "portrait",
                    Margins = new Margins { Top = 20, Bottom = 20, Left = 15, Right = 15 }
                },
                Typography = new Typography
                {
                    PrimaryFont = "Arial",
                    SecondaryFont = "Helvetica",
                    HeadingSize = 16,
                    BodySize = 12,
                    LineHeight = 1.5
                },
                Colors = new Colors
                {
                    Primary = "#2E86AB",
                    Secondary = "#A23B72",
                    Accent = "#F18F01",
                    Background = "#FFFFFF",
                    Border = "#E0E0E0"
                }
            };
        }

        private StylesData CreateStylesFromReportStyle(ReportStyle reportStyle)
        {
            // Transform existing ReportStyle to new StylesData format
            return new StylesData
            {
                Theme = new ThemeInfo
                {
                    Name = reportStyle.Name ?? "Custom",
                    Description = "Custom report styling",
                    BaseTheme = "custom"
                },
                Layout = new LayoutConfig
                {
                    PageSize = "A4",
                    Orientation = "portrait",
                    Margins = new Margins { Top = 20, Bottom = 20, Left = 15, Right = 15 }
                },
                Typography = new Typography
                {
                    PrimaryFont = "Arial",
                    SecondaryFont = "Helvetica",
                    HeadingSize = 16,
                    BodySize = 12,
                    LineHeight = 1.5
                },
                Colors = new Colors
                {
                    Primary = "#2E86AB",
                    Secondary = "#A23B72",
                    Accent = "#F18F01",
                    Background = "#FFFFFF",
                    Border = "#E0E0E0"
                }
            };
        }
    }
}
```

## Phase 4: Integration Testing Guidelines

### 4.1 Testing Strategy

#### Unit Testing
- Test each seeder independently
- Mock external dependencies (Cosmos DB, Blob Storage)
- Validate data transformation logic
- Test error handling and recovery

#### Integration Testing
- Test full seeding pipeline
- Validate cross-storage references
- Test with real storage services
- Performance testing with large datasets

#### Validation Testing
- GUID format validation
- Foreign key integrity validation
- Data consistency across storage systems
- Template inheritance validation

### 4.2 Test Data Requirements

#### Minimal Test Dataset
- 2 Tenant Profiles
- 5 Clients per tenant
- 10 Reports per tenant
- 2-3 Versions per report
- 3-5 Sections per report
- 5-10 Fields per section

#### Performance Test Dataset
- 10 Tenant Profiles
- 100 Clients per tenant
- 1000 Reports per tenant
- 5 Versions per report
- 10 Sections per report
- 20 Fields per section

### 4.3 Validation Checklist

#### Data Integrity
- [ ] All GUIDs are valid format
- [ ] All foreign key relationships are valid
- [ ] No orphaned records
- [ ] Data consistency across storage systems

#### Storage Strategy
- [ ] SQL entities created correctly
- [ ] Cosmos DB documents created with proper structure
- [ ] Blob storage files created in correct hierarchy
- [ ] Storage metadata updated accurately

#### Template Inheritance
- [ ] Template source tracking works
- [ ] Modification flags set correctly
- [ ] Inheritance rules applied properly
- [ ] Template updates propagate correctly

#### Performance
- [ ] Seeding completes within acceptable time
- [ ] Memory usage stays within limits
- [ ] No resource leaks
- [ ] Proper error handling and recovery

This completes the comprehensive DataSeeder implementation plan for the multi-storage architecture with draft-based editing and template inheritance support.
