using System.Text.Json.Serialization;

namespace FY.WB.CSHero2.Application.Models.Migration
{
    /// <summary>
    /// Configuration options for migration operations
    /// </summary>
    public class MigrationOptions
    {
        /// <summary>
        /// Number of reports to process in each batch
        /// </summary>
        public int BatchSize { get; set; } = 10;

        /// <summary>
        /// Maximum number of concurrent operations
        /// </summary>
        public int MaxConcurrency { get; set; } = 3;

        /// <summary>
        /// Whether to perform a dry run without making changes
        /// </summary>
        public bool DryRun { get; set; } = false;

        /// <summary>
        /// Whether to skip validation steps for faster migration
        /// </summary>
        public bool SkipValidation { get; set; } = false;

        /// <summary>
        /// Whether to continue migration on errors
        /// </summary>
        public bool ContinueOnError { get; set; } = true;

        /// <summary>
        /// Maximum retry attempts for failed operations
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in milliseconds
        /// </summary>
        public int RetryDelayMs { get; set; } = 1000;

        /// <summary>
        /// Whether to create backups before migration
        /// </summary>
        public bool CreateBackups { get; set; } = true;

        /// <summary>
        /// Specific tenant ID to migrate (null for all tenants)
        /// </summary>
        public Guid? TenantId { get; set; }

        /// <summary>
        /// Whether to migrate only reports without existing multi-storage references
        /// </summary>
        public bool MigrateOnlyUnmigrated { get; set; } = true;

        /// <summary>
        /// Custom timeout for individual operations in seconds
        /// </summary>
        public int OperationTimeoutSeconds { get; set; } = 300;
    }

    /// <summary>
    /// Result of a migration operation
    /// </summary>
    public class MigrationResult
    {
        /// <summary>
        /// Unique identifier for this migration operation
        /// </summary>
        public Guid OperationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Whether the migration was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Total number of reports processed
        /// </summary>
        public int TotalReports { get; set; }

        /// <summary>
        /// Number of reports successfully migrated
        /// </summary>
        public int SuccessfulMigrations { get; set; }

        /// <summary>
        /// Number of reports that failed migration
        /// </summary>
        public int FailedMigrations { get; set; }

        /// <summary>
        /// Number of reports skipped (already migrated)
        /// </summary>
        public int SkippedReports { get; set; }

        /// <summary>
        /// Total time taken for the migration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// When the migration started
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// When the migration completed
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// List of errors encountered during migration
        /// </summary>
        public List<MigrationError> Errors { get; set; } = new List<MigrationError>();

        /// <summary>
        /// List of warnings generated during migration
        /// </summary>
        public List<MigrationWarning> Warnings { get; set; } = new List<MigrationWarning>();

        /// <summary>
        /// Detailed results for each report
        /// </summary>
        public List<ReportMigrationResult> ReportResults { get; set; } = new List<ReportMigrationResult>();

        /// <summary>
        /// Migration statistics
        /// </summary>
        public MigrationStatistics Statistics { get; set; } = new MigrationStatistics();
    }

    /// <summary>
    /// Result of migrating a specific report
    /// </summary>
    public class ReportMigrationResult
    {
        /// <summary>
        /// Report ID that was migrated
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Report name for reference
        /// </summary>
        public string ReportName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the report migration was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of versions migrated for this report
        /// </summary>
        public int VersionsMigrated { get; set; }

        /// <summary>
        /// Cosmos DB document ID created
        /// </summary>
        public string? CosmosDocumentId { get; set; }

        /// <summary>
        /// Blob storage ID created
        /// </summary>
        public string? BlobStorageId { get; set; }

        /// <summary>
        /// Time taken to migrate this report
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Errors specific to this report
        /// </summary>
        public List<MigrationError> Errors { get; set; } = new List<MigrationError>();

        /// <summary>
        /// Warnings specific to this report
        /// </summary>
        public List<MigrationWarning> Warnings { get; set; } = new List<MigrationWarning>();

        /// <summary>
        /// Version-specific results
        /// </summary>
        public List<VersionMigrationResult> VersionResults { get; set; } = new List<VersionMigrationResult>();
    }

    /// <summary>
    /// Result of migrating a specific report version
    /// </summary>
    public class VersionMigrationResult
    {
        /// <summary>
        /// Version ID that was migrated
        /// </summary>
        public Guid VersionId { get; set; }

        /// <summary>
        /// Version number for reference
        /// </summary>
        public int VersionNumber { get; set; }

        /// <summary>
        /// Whether the version migration was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of sections migrated
        /// </summary>
        public int SectionsMigrated { get; set; }

        /// <summary>
        /// Number of fields migrated
        /// </summary>
        public int FieldsMigrated { get; set; }

        /// <summary>
        /// Number of components migrated
        /// </summary>
        public int ComponentsMigrated { get; set; }

        /// <summary>
        /// Size of original JSON data in bytes
        /// </summary>
        public long OriginalDataSize { get; set; }

        /// <summary>
        /// Size of migrated data in bytes
        /// </summary>
        public long MigratedDataSize { get; set; }

        /// <summary>
        /// Errors specific to this version
        /// </summary>
        public List<MigrationError> Errors { get; set; } = new List<MigrationError>();
    }

    /// <summary>
    /// Current status of migration operations
    /// </summary>
    public class MigrationStatus
    {
        /// <summary>
        /// Whether any migration is currently running
        /// </summary>
        public bool IsRunning { get; set; }

        /// <summary>
        /// Current operation ID if running
        /// </summary>
        public Guid? CurrentOperationId { get; set; }

        /// <summary>
        /// Current migration progress
        /// </summary>
        public MigrationProgress? CurrentProgress { get; set; }

        /// <summary>
        /// List of recent migration operations
        /// </summary>
        public List<MigrationOperationSummary> RecentOperations { get; set; } = new List<MigrationOperationSummary>();

        /// <summary>
        /// Overall migration statistics
        /// </summary>
        public MigrationStatistics OverallStatistics { get; set; } = new MigrationStatistics();

        /// <summary>
        /// System health status for migration
        /// </summary>
        public MigrationSystemHealth SystemHealth { get; set; } = new MigrationSystemHealth();
    }

    /// <summary>
    /// Progress information for an ongoing migration
    /// </summary>
    public class MigrationProgress
    {
        /// <summary>
        /// Operation ID
        /// </summary>
        public Guid OperationId { get; set; }

        /// <summary>
        /// Current phase of migration
        /// </summary>
        public MigrationPhase CurrentPhase { get; set; }

        /// <summary>
        /// Total number of reports to migrate
        /// </summary>
        public int TotalReports { get; set; }

        /// <summary>
        /// Number of reports completed
        /// </summary>
        public int CompletedReports { get; set; }

        /// <summary>
        /// Number of reports currently being processed
        /// </summary>
        public int InProgressReports { get; set; }

        /// <summary>
        /// Number of reports failed
        /// </summary>
        public int FailedReports { get; set; }

        /// <summary>
        /// Percentage complete (0-100)
        /// </summary>
        public double PercentComplete => TotalReports > 0 ? (double)CompletedReports / TotalReports * 100 : 0;

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }

        /// <summary>
        /// Current processing rate (reports per minute)
        /// </summary>
        public double ProcessingRate { get; set; }

        /// <summary>
        /// When the operation started
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Last update time
        /// </summary>
        public DateTime LastUpdateTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Current status message
        /// </summary>
        public string StatusMessage { get; set; } = string.Empty;

        /// <summary>
        /// Currently processing report (if any)
        /// </summary>
        public ReportMigrationInfo? CurrentReport { get; set; }
    }

    /// <summary>
    /// Migration operation summary
    /// </summary>
    public class MigrationOperationSummary
    {
        /// <summary>
        /// Operation ID
        /// </summary>
        public Guid OperationId { get; set; }

        /// <summary>
        /// Operation type
        /// </summary>
        public MigrationOperationType OperationType { get; set; }

        /// <summary>
        /// When the operation started
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// When the operation ended
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Operation status
        /// </summary>
        public MigrationOperationStatus Status { get; set; }

        /// <summary>
        /// Number of reports processed
        /// </summary>
        public int ReportsProcessed { get; set; }

        /// <summary>
        /// Number of successful migrations
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// Number of failed migrations
        /// </summary>
        public int FailureCount { get; set; }

        /// <summary>
        /// Total duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// User who initiated the operation
        /// </summary>
        public string InitiatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// Migration statistics and metrics
    /// </summary>
    public class MigrationStatistics
    {
        /// <summary>
        /// Total number of reports in the system
        /// </summary>
        public int TotalReports { get; set; }

        /// <summary>
        /// Number of reports successfully migrated
        /// </summary>
        public int MigratedReports { get; set; }

        /// <summary>
        /// Number of reports pending migration
        /// </summary>
        public int PendingReports { get; set; }

        /// <summary>
        /// Number of reports with migration failures
        /// </summary>
        public int FailedReports { get; set; }

        /// <summary>
        /// Total number of report versions
        /// </summary>
        public int TotalVersions { get; set; }

        /// <summary>
        /// Number of versions successfully migrated
        /// </summary>
        public int MigratedVersions { get; set; }

        /// <summary>
        /// Total size of data migrated in bytes
        /// </summary>
        public long TotalDataMigrated { get; set; }

        /// <summary>
        /// Average migration time per report
        /// </summary>
        public TimeSpan AverageMigrationTime { get; set; }

        /// <summary>
        /// Peak migration rate (reports per hour)
        /// </summary>
        public double PeakMigrationRate { get; set; }

        /// <summary>
        /// Number of Cosmos DB documents created
        /// </summary>
        public int CosmosDocumentsCreated { get; set; }

        /// <summary>
        /// Number of blob storage containers created
        /// </summary>
        public int BlobContainersCreated { get; set; }

        /// <summary>
        /// Last migration operation time
        /// </summary>
        public DateTime? LastMigrationTime { get; set; }

        /// <summary>
        /// Migration success rate percentage
        /// </summary>
        public double SuccessRate => TotalReports > 0 ? (double)MigratedReports / TotalReports * 100 : 0;
    }

    /// <summary>
    /// System health status for migration operations
    /// </summary>
    public class MigrationSystemHealth
    {
        /// <summary>
        /// SQL database connectivity status
        /// </summary>
        public HealthStatus SqlDatabaseStatus { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// Cosmos DB connectivity status
        /// </summary>
        public HealthStatus CosmosDbStatus { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// Blob storage connectivity status
        /// </summary>
        public HealthStatus BlobStorageStatus { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// Overall system health
        /// </summary>
        public HealthStatus OverallStatus { get; set; } = HealthStatus.Unknown;

        /// <summary>
        /// Available storage capacity percentage
        /// </summary>
        public double AvailableStorageCapacity { get; set; }

        /// <summary>
        /// Current system load percentage
        /// </summary>
        public double SystemLoad { get; set; }

        /// <summary>
        /// Last health check time
        /// </summary>
        public DateTime LastHealthCheck { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Health check details
        /// </summary>
        public List<HealthCheckDetail> HealthDetails { get; set; } = new List<HealthCheckDetail>();
    }

    /// <summary>
    /// Information about a report that needs migration
    /// </summary>
    public class ReportMigrationInfo
    {
        /// <summary>
        /// Report ID
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Report name
        /// </summary>
        public string ReportName { get; set; } = string.Empty;

        /// <summary>
        /// Tenant ID
        /// </summary>
        public Guid TenantId { get; set; }

        /// <summary>
        /// Number of versions for this report
        /// </summary>
        public int VersionCount { get; set; }

        /// <summary>
        /// Estimated data size in bytes
        /// </summary>
        public long EstimatedDataSize { get; set; }

        /// <summary>
        /// Whether the report has been migrated
        /// </summary>
        public bool IsMigrated { get; set; }

        /// <summary>
        /// Migration priority (1-10, 10 being highest)
        /// </summary>
        public int Priority { get; set; } = 5;

        /// <summary>
        /// Last modification time
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// Migration complexity score
        /// </summary>
        public MigrationComplexity Complexity { get; set; } = MigrationComplexity.Medium;

        /// <summary>
        /// Potential migration issues
        /// </summary>
        public List<string> PotentialIssues { get; set; } = new List<string>();
    }

    /// <summary>
    /// Migration error information
    /// </summary>
    public class MigrationError
    {
        /// <summary>
        /// Error ID for tracking
        /// </summary>
        public Guid ErrorId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Error code for categorization
        /// </summary>
        public string ErrorCode { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Detailed error description
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// Error severity level
        /// </summary>
        public ErrorSeverity Severity { get; set; } = ErrorSeverity.Error;

        /// <summary>
        /// When the error occurred
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Report ID associated with the error
        /// </summary>
        public Guid? ReportId { get; set; }

        /// <summary>
        /// Version ID associated with the error
        /// </summary>
        public Guid? VersionId { get; set; }

        /// <summary>
        /// Stack trace if available
        /// </summary>
        public string? StackTrace { get; set; }

        /// <summary>
        /// Whether this error is recoverable
        /// </summary>
        public bool IsRecoverable { get; set; } = true;

        /// <summary>
        /// Suggested resolution steps
        /// </summary>
        public List<string> ResolutionSteps { get; set; } = new List<string>();
    }

    /// <summary>
    /// Migration warning information
    /// </summary>
    public class MigrationWarning
    {
        /// <summary>
        /// Warning ID for tracking
        /// </summary>
        public Guid WarningId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Warning code for categorization
        /// </summary>
        public string WarningCode { get; set; } = string.Empty;

        /// <summary>
        /// Warning message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// When the warning occurred
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Report ID associated with the warning
        /// </summary>
        public Guid? ReportId { get; set; }

        /// <summary>
        /// Version ID associated with the warning
        /// </summary>
        public Guid? VersionId { get; set; }

        /// <summary>
        /// Recommended actions
        /// </summary>
        public List<string> RecommendedActions { get; set; } = new List<string>();
    }

    /// <summary>
    /// Migration history entry
    /// </summary>
    public class MigrationHistoryEntry
    {
        /// <summary>
        /// History entry ID
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Report ID
        /// </summary>
        public Guid ReportId { get; set; }

        /// <summary>
        /// Operation type
        /// </summary>
        public MigrationOperationType OperationType { get; set; }

        /// <summary>
        /// When the operation occurred
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Operation result
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Operation details
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// User who performed the operation
        /// </summary>
        public string PerformedBy { get; set; } = string.Empty;

        /// <summary>
        /// Duration of the operation
        /// </summary>
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Result of data transformation operations
    /// </summary>
    public class ReportDataTransformResult
    {
        /// <summary>
        /// Whether the transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformed report data
        /// </summary>
        public MultiStorage.ReportData? ReportData { get; set; }

        /// <summary>
        /// Number of sections extracted
        /// </summary>
        public int SectionsExtracted { get; set; }

        /// <summary>
        /// Number of fields extracted
        /// </summary>
        public int FieldsExtracted { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of component transformation operations
    /// </summary>
    public class ComponentTransformResult
    {
        /// <summary>
        /// Whether the transformation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Transformed components
        /// </summary>
        public List<MultiStorage.ReportComponent> Components { get; set; } = new List<MultiStorage.ReportComponent>();

        /// <summary>
        /// Number of components extracted
        /// </summary>
        public int ComponentsExtracted { get; set; }

        /// <summary>
        /// Transformation errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of section transformation
    /// </summary>
    public class ReportSectionTransformResult
    {
        /// <summary>
        /// Transformed section
        /// </summary>
        public MultiStorage.ReportSection Section { get; set; } = new MultiStorage.ReportSection();

        /// <summary>
        /// Number of fields in the section
        /// </summary>
        public int FieldCount { get; set; }

        /// <summary>
        /// Section transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Result of field transformation
    /// </summary>
    public class ReportFieldTransformResult
    {
        /// <summary>
        /// Transformed field
        /// </summary>
        public MultiStorage.ReportSectionField Field { get; set; } = new MultiStorage.ReportSectionField();

        /// <summary>
        /// Field transformation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Validation result for migration operations
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Whether validation passed
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Validation errors
        /// </summary>
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();

        /// <summary>
        /// Validation warnings
        /// </summary>
        public List<ValidationWarning> Warnings { get; set; } = new List<ValidationWarning>();

        /// <summary>
        /// Validation details
        /// </summary>
        public ValidationDetails Details { get; set; } = new ValidationDetails();
    }

    /// <summary>
    /// Validation error
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// Error code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Error message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Field or property that failed validation
        /// </summary>
        public string? Field { get; set; }

        /// <summary>
        /// Error severity
        /// </summary>
        public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;
    }

    /// <summary>
    /// Validation warning
    /// </summary>
    public class ValidationWarning
    {
        /// <summary>
        /// Warning code
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// Warning message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Field or property that generated the warning
        /// </summary>
        public string? Field { get; set; }
    }

    /// <summary>
    /// Detailed validation information
    /// </summary>
    public class ValidationDetails
    {
        /// <summary>
        /// Data integrity check results
        /// </summary>
        public bool DataIntegrityValid { get; set; }

        /// <summary>
        /// Cross-reference validation results
        /// </summary>
        public bool CrossReferencesValid { get; set; }

        /// <summary>
        /// Storage connectivity validation results
        /// </summary>
        public bool StorageConnectivityValid { get; set; }

        /// <summary>
        /// Schema validation results
        /// </summary>
        public bool SchemaValid { get; set; }

        /// <summary>
        /// Performance validation results
        /// </summary>
        public bool PerformanceValid { get; set; }

        /// <summary>
        /// Validation timestamp
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Rollback operation result
    /// </summary>
    public class RollbackResult
    {
        /// <summary>
        /// Whether rollback was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of items rolled back
        /// </summary>
        public int ItemsRolledBack { get; set; }

        /// <summary>
        /// Rollback duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Rollback errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Rollback warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Rollback timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Dry run operation result
    /// </summary>
    public class DryRunResult
    {
        /// <summary>
        /// Number of reports that would be migrated
        /// </summary>
        public int ReportsToMigrate { get; set; }

        /// <summary>
        /// Number of versions that would be migrated
        /// </summary>
        public int VersionsToMigrate { get; set; }

        /// <summary>
        /// Estimated data size to migrate
        /// </summary>
        public long EstimatedDataSize { get; set; }

        /// <summary>
        /// Estimated migration duration
        /// </summary>
        public TimeSpan EstimatedDuration { get; set; }

        /// <summary>
        /// Potential issues identified
        /// </summary>
        public List<string> PotentialIssues { get; set; } = new List<string>();

        /// <summary>
        /// Recommendations for migration
        /// </summary>
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// Dry run timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Migration cancellation result
    /// </summary>
    public class CancellationResult
    {
        /// <summary>
        /// Whether cancellation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Cancellation message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Number of operations cancelled
        /// </summary>
        public int OperationsCancelled { get; set; }

        /// <summary>
        /// Cancellation timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Cleanup operation result
    /// </summary>
    public class CleanupResult
    {
        /// <summary>
        /// Whether cleanup was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Number of items cleaned up
        /// </summary>
        public int ItemsCleanedUp { get; set; }

        /// <summary>
        /// Amount of storage freed in bytes
        /// </summary>
        public long StorageFreed { get; set; }

        /// <summary>
        /// Cleanup duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Cleanup errors
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Cleanup timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Health check detail
    /// </summary>
    public class HealthCheckDetail
    {
        /// <summary>
        /// Component name
        /// </summary>
        public string Component { get; set; } = string.Empty;

        /// <summary>
        /// Health status
        /// </summary>
        public HealthStatus Status { get; set; }

        /// <summary>
        /// Status message
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Response time in milliseconds
        /// </summary>
        public double ResponseTimeMs { get; set; }

        /// <summary>
        /// Check timestamp
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Additional validation result types
    /// </summary>
    public class ReferenceValidationResult : ValidationResult
    {
        /// <summary>
        /// SQL to Cosmos DB reference validation
        /// </summary>
        public bool SqlToCosmosReferencesValid { get; set; }

        /// <summary>
        /// Cosmos DB to Blob Storage reference validation
        /// </summary>
        public bool CosmosToBlob ReferencesValid { get; set; }

        /// <summary>
        /// Blob Storage to SQL reference validation
        /// </summary>
        public bool BlobToSqlReferencesValid { get; set; }

        /// <summary>
        /// Cross-storage consistency validation
        /// </summary>
        public bool CrossStorageConsistencyValid { get; set; }
    }

    /// <summary>
    /// Performance validation result
    /// </summary>
    public class PerformanceValidationResult : ValidationResult
    {
        /// <summary>
        /// Query performance validation
        /// </summary>
        public bool QueryPerformanceValid { get; set; }

        /// <summary>
        /// Storage performance validation
        /// </summary>
        public bool StoragePerformanceValid { get; set; }

        /// <summary>
        /// Network latency validation
        /// </summary>
        public bool NetworkLatencyValid { get; set; }

        /// <summary>
        /// Throughput validation
        /// </summary>
        public bool ThroughputValid { get; set; }
    }

    /// <summary>
    /// Security validation result
    /// </summary>
    public class SecurityValidationResult : ValidationResult
    {
        /// <summary>
        /// Access control validation
        /// </summary>
        public bool AccessControlValid { get; set; }

        /// <summary>
        /// Data encryption validation
        /// </summary>
        public bool DataEncryptionValid { get; set; }

        /// <summary>
        /// Audit trail validation
        /// </summary>
        public bool AuditTrailValid { get; set; }

        /// <summary>
        /// Compliance validation
        /// </summary>
        public bool ComplianceValid { get; set; }
    }

    // Enumerations

    /// <summary>
    /// Migration operation phases
    /// </summary>
    public enum MigrationPhase
    {
        Initializing,
        ValidatingData,
        TransformingData,
        MigratingToCosmosDb,
        MigratingToBlobStorage,
        UpdatingReferences,
        ValidatingMigration,
        Finalizing,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// Types of migration operations
    /// </summary>
    public enum MigrationOperationType
    {
        FullMigration,
        PartialMigration,
        DataTransformation,
        ValidationOnly,
        Rollback,
        Cleanup,
        DryRun,
        HealthCheck
    }

    /// <summary>
    /// Migration operation status
    /// </summary>
    public enum MigrationOperationStatus
    {
        NotStarted,
        InProgress,
        Completed,
        Failed,
        Cancelled,
        PartiallyCompleted
    }

    /// <summary>
    /// Health status enumeration
    /// </summary>
    public enum HealthStatus
    {
        Unknown,
        Healthy,
        Degraded,
        Unhealthy,
        Critical
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        Info,
        Warning,
        Error,
        Critical,
        Fatal
    }

    /// <summary>
    /// Validation severity levels
    /// </summary>
    public enum ValidationSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    /// <summary>
    /// Migration complexity levels
    /// </summary>
    public enum MigrationComplexity
    {
        Low,
        Medium,
        High,
        VeryHigh
    }

    // Multi-storage namespace for data models
    namespace MultiStorage
    {
        /// <summary>
        /// Report data structure for multi-storage
        /// </summary>
        public class ReportData
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public List<ReportSection> Sections { get; set; } = new();
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// Report section for multi-storage
        /// </summary>
        public class ReportSection
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public int Order { get; set; }
            public List<ReportSectionField> Fields { get; set; } = new();
        }

        /// <summary>
        /// Report section field for multi-storage
        /// </summary>
        public class ReportSectionField
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public string Value { get; set; } = string.Empty;
            public int Order { get; set; }
        }

        /// <summary>
        /// Report component for multi-storage
        /// </summary>
        public class ReportComponent
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public Dictionary<string, object> Properties { get; set; } = new();
            public string Content { get; set; } = string.Empty;
        }
    }
}
