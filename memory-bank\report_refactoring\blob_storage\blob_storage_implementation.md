# Blob Storage Implementation for Multi-Storage Architecture

## Overview

This document provides the complete implementation plan for blob storage integration in the multi-storage architecture, supporting versioned components, styles, and template inheritance.

## Implementation Structure

### 1. Directory Organization

```
memory-bank/report_refactoring/blob_storage/
├── implementation/
│   ├── BlobStorageService.cs
│   ├── BlobStorageModels.cs
│   ├── BlobSeederEnhanced.cs
│   └── BlobStorageConfiguration.cs
├── interfaces/
│   ├── IBlobStorageService.cs
│   ├── IBlobSeeder.cs
│   └── IBlobStorageConfiguration.cs
├── tests/
│   ├── BlobStorageServiceTests.cs
│   ├── BlobSeederTests.cs
│   └── IntegrationTests.cs
└── documentation/
    ├── blob_storage_api.md
    ├── blob_hierarchy_design.md
    └── performance_guidelines.md
```

### 2. Core Interfaces

#### IBlobStorageService Interface

```csharp
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace FY.WB.CSHero2.Application.Interfaces.Storage
{
    /// <summary>
    /// Interface for blob storage operations supporting multi-tenant hierarchical storage
    /// </summary>
    public interface IBlobStorageService
    {
        /// <summary>
        /// Upload content to blob storage
        /// </summary>
        Task<string> UploadAsync(string blobPath, string content, string contentType = "application/json", CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Upload stream to blob storage
        /// </summary>
        Task<string> UploadAsync(string blobPath, Stream content, string contentType = "application/octet-stream", CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Download content from blob storage
        /// </summary>
        Task<string> DownloadAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Download stream from blob storage
        /// </summary>
        Task<Stream> DownloadStreamAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Check if blob exists
        /// </summary>
        Task<bool> ExistsAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Delete blob
        /// </summary>
        Task<bool> DeleteAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// List blobs with optional prefix filter
        /// </summary>
        Task<IEnumerable<string>> ListBlobsAsync(string prefix = "", CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Get blob metadata
        /// </summary>
        Task<BlobMetadata> GetMetadataAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Copy blob to new location
        /// </summary>
        Task<bool> CopyAsync(string sourcePath, string destinationPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Generate SAS URL for blob access
        /// </summary>
        Task<string> GenerateSasUrlAsync(string blobPath, TimeSpan expiry, BlobSasPermissions permissions, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Batch upload multiple blobs
        /// </summary>
        Task<BatchUploadResult> BatchUploadAsync(IEnumerable<BlobUploadRequest> requests, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Get blob size in bytes
        /// </summary>
        Task<long> GetSizeAsync(string blobPath, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Create blob container if it doesn't exist
        /// </summary>
        Task<bool> CreateContainerAsync(string containerName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Delete blob container
        /// </summary>
        Task<bool> DeleteContainerAsync(string containerName, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Blob metadata information
    /// </summary>
    public class BlobMetadata
    {
        public string Name { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastModified { get; set; }
        public string ETag { get; set; } = string.Empty;
        public Dictionary<string, string> Properties { get; set; } = new();
    }

    /// <summary>
    /// SAS permissions for blob access
    /// </summary>
    [Flags]
    public enum BlobSasPermissions
    {
        None = 0,
        Read = 1,
        Write = 2,
        Delete = 4,
        List = 8,
        Add = 16,
        Create = 32,
        All = Read | Write | Delete | List | Add | Create
    }

    /// <summary>
    /// Blob upload request for batch operations
    /// </summary>
    public class BlobUploadRequest
    {
        public string BlobPath { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public Stream? ContentStream { get; set; }
        public string ContentType { get; set; } = "application/json";
        public Dictionary<string, string> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Result of batch upload operation
    /// </summary>
    public class BatchUploadResult
    {
        public int TotalRequests { get; set; }
        public int SuccessfulUploads { get; set; }
        public int FailedUploads { get; set; }
        public List<string> SuccessfulPaths { get; set; } = new();
        public List<BatchUploadError> Errors { get; set; } = new();
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// Batch upload error information
    /// </summary>
    public class BatchUploadError
    {
        public string BlobPath { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
    }
}
```

#### IBlobSeeder Interface

```csharp
using System.Threading;
using System.Threading.Tasks;
using FY.WB.CSHero2.Infrastructure.Persistence;

namespace FY.WB.CSHero2.Application.Interfaces.Seeding
{
    /// <summary>
    /// Interface for blob storage seeding operations
    /// </summary>
    public interface IBlobSeeder
    {
        /// <summary>
        /// Seed blob storage with initial data
        /// </summary>
        Task SeedAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Seed system templates to blob storage
        /// </summary>
        Task SeedSystemTemplatesAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Seed default styles to blob storage
        /// </summary>
        Task SeedDefaultStylesAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Seed tenant-specific templates
        /// </summary>
        Task SeedTenantTemplatesAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Seed report version blobs (components and styles)
        /// </summary>
        Task SeedReportVersionBlobsAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Get existing blob IDs to avoid duplicates
        /// </summary>
        Task<List<string>> GetExistingBlobIdsAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Validate blob storage health
        /// </summary>
        Task<BlobStorageHealthResult> ValidateHealthAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// Clean up orphaned blobs
        /// </summary>
        Task<BlobCleanupResult> CleanupOrphanedBlobsAsync(ApplicationDbContext context, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Blob storage health check result
    /// </summary>
    public class BlobStorageHealthResult
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan ResponseTime { get; set; }
        public long AvailableSpace { get; set; }
        public long UsedSpace { get; set; }
        public int BlobCount { get; set; }
        public List<string> Issues { get; set; } = new();
        public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Blob cleanup operation result
    /// </summary>
    public class BlobCleanupResult
    {
        public bool Success { get; set; }
        public int OrphanedBlobsFound { get; set; }
        public int BlobsDeleted { get; set; }
        public long SpaceFreed { get; set; }
        public TimeSpan Duration { get; set; }
        public List<string> DeletedBlobs { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public DateTime CleanupTime { get; set; } = DateTime.UtcNow;
    }
}
```

### 3. Configuration Interface

```csharp
namespace FY.WB.CSHero2.Application.Interfaces.Configuration
{
    /// <summary>
    /// Configuration interface for blob storage settings
    /// </summary>
    public interface IBlobStorageConfiguration
    {
        /// <summary>
        /// Blob storage connection string
        /// </summary>
        string ConnectionString { get; }
        
        /// <summary>
        /// Default container name for report components
        /// </summary>
        string DefaultContainerName { get; }
        
        /// <summary>
        /// Maximum blob size in bytes
        /// </summary>
        long MaxBlobSize { get; }
        
        /// <summary>
        /// Default SAS token expiry time
        /// </summary>
        TimeSpan DefaultSasExpiry { get; }
        
        /// <summary>
        /// Enable blob storage compression
        /// </summary>
        bool EnableCompression { get; }
        
        /// <summary>
        /// Enable blob storage encryption
        /// </summary>
        bool EnableEncryption { get; }
        
        /// <summary>
        /// Blob storage retry policy settings
        /// </summary>
        BlobRetryPolicy RetryPolicy { get; }
        
        /// <summary>
        /// CDN endpoint for blob access
        /// </summary>
        string? CdnEndpoint { get; }
        
        /// <summary>
        /// Enable development mode (use Azurite)
        /// </summary>
        bool UseDevelopmentStorage { get; }
        
        /// <summary>
        /// Development storage connection string
        /// </summary>
        string? DevelopmentStorageConnectionString { get; }
    }

    /// <summary>
    /// Blob storage retry policy configuration
    /// </summary>
    public class BlobRetryPolicy
    {
        public int MaxRetries { get; set; } = 3;
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
        public double BackoffMultiplier { get; set; } = 2.0;
    }
}
```

## Implementation Benefits

### 1. Scalability
- **Hierarchical Storage**: Organized tenant/report/version structure
- **Batch Operations**: Efficient bulk upload/download capabilities
- **CDN Integration**: Fast global content delivery
- **Compression**: Reduced storage costs and transfer times

### 2. Security
- **SAS Tokens**: Secure, time-limited access to blobs
- **Encryption**: Data protection at rest and in transit
- **Multi-tenant Isolation**: Clear separation of tenant data
- **Access Control**: Fine-grained permissions management

### 3. Reliability
- **Retry Policies**: Automatic retry on transient failures
- **Health Monitoring**: Continuous storage health validation
- **Backup Support**: Easy backup and restore operations
- **Error Handling**: Comprehensive error reporting and recovery

### 4. Development Experience
- **Azurite Support**: Local development with Azure Storage Emulator
- **Comprehensive Testing**: Unit and integration test support
- **Logging**: Detailed operation logging for debugging
- **Configuration**: Flexible configuration for different environments

This blob storage implementation provides a robust foundation for the multi-storage architecture while supporting the draft-based editing workflow and template inheritance model.
